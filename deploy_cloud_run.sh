#!/bin/bash

# SafeHaven Emergency Management Dashboard
# Cloud Run Deployment Script
# Enhanced deployment script with better error handling and configuration

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-safehaven-463909}"
REGION="${REGION:-us-central1}"
SERVICE_NAME="${SERVICE_NAME:-safehaven-dashboard}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Artifact Registry configuration
ARTIFACT_REPO_NAME="safehaven-repo"
IMAGE_NAME="safehaven-dashboard"
IMAGE_TAG="${IMAGE_TAG:-latest}"
IMAGE_URL="${REGION}-docker.pkg.dev/${PROJECT_ID}/${ARTIFACT_REPO_NAME}/${IMAGE_NAME}:${IMAGE_TAG}"

# Service account configuration
SERVICE_ACCOUNT_EMAIL="safehaven-dashboard@${PROJECT_ID}.iam.gserviceaccount.com"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        exit 1
    fi

    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "No active gcloud authentication found"
        exit 1
    fi

    # Set the project
    gcloud config set project "$PROJECT_ID"
    log_success "Prerequisites check completed"
}

# Deploy to Cloud Run
deploy_service() {
    log_info "Deploying to Cloud Run..."

    # Set environment-specific configuration
    local min_instances=1
    local max_instances=100
    local cpu="2"
    local memory="2Gi"
    local concurrency=80
    local timeout="300s"

    if [ "$ENVIRONMENT" = "staging" ]; then
        SERVICE_NAME="safehaven-dashboard-staging"
        min_instances=0
        max_instances=10
        cpu="1"
        memory="1Gi"
        concurrency=50
    fi

    log_info "Deploying service: $SERVICE_NAME"
    log_info "Environment: $ENVIRONMENT"
    log_info "Image: $IMAGE_URL"
    log_info "Service Account: $SERVICE_ACCOUNT_EMAIL"

    gcloud run deploy "$SERVICE_NAME" \
        --image="$IMAGE_URL" \
        --platform="managed" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --service-account="$SERVICE_ACCOUNT_EMAIL" \
        --allow-unauthenticated \
        --port=8080 \
        --min-instances="$min_instances" \
        --max-instances="$max_instances" \
        --cpu="$cpu" \
        --memory="$memory" \
        --concurrency="$concurrency" \
        --timeout="$timeout" \
        --execution-environment=gen2 \
        --cpu-throttling \
        --session-affinity \
        --set-env-vars="NODE_ENV=$ENVIRONMENT,PORT=8080" \
        --tag="$ENVIRONMENT" \
        --quiet

    log_success "Service deployed successfully"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."

    # Get service URL
    local service_url
    service_url=$(gcloud run services describe "$SERVICE_NAME" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format='value(status.url)')

    if [ -z "$service_url" ]; then
        log_error "Failed to get service URL"
        return 1
    fi

    log_info "Service URL: $service_url"

    # Wait for service to be ready
    log_info "Waiting for service to be ready..."
    sleep 30

    # Health check
    local health_url="${service_url}/health"
    if curl -f -s "$health_url" > /dev/null; then
        log_success "Health check passed"
        log_success "Service is running at: $service_url"
    else
        log_error "Health check failed"
        log_error "Check service logs: gcloud logs read --service=$SERVICE_NAME"
        return 1
    fi
}

# Display deployment summary
display_summary() {
    local service_url
    service_url=$(gcloud run services describe "$SERVICE_NAME" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format='value(status.url)')

    echo
    echo "=== Deployment Summary ==="
    echo "Service Name: $SERVICE_NAME"
    echo "Environment: $ENVIRONMENT"
    echo "Project: $PROJECT_ID"
    echo "Region: $REGION"
    echo "Image: $IMAGE_URL"
    echo "Service URL: $service_url"
    echo "Service Account: $SERVICE_ACCOUNT_EMAIL"
    echo
    echo "=== Next Steps ==="
    echo "1. Test the application: curl $service_url/health"
    echo "2. View logs: gcloud logs read --service=$SERVICE_NAME"
    echo "3. Monitor metrics in Cloud Console"
    echo "4. Set up custom domain if needed"
    echo
}

# Main execution
main() {
    log_info "Starting SafeHaven Cloud Run deployment..."

    check_prerequisites
    deploy_service
    verify_deployment
    display_summary

    log_success "Deployment completed successfully!"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
