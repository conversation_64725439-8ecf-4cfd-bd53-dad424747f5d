name: SafeHaven Emergency Dashboard - CI/CD Pipeline

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      skip_tests:
        description: 'Skip tests'
        required: false
        default: false
        type: boolean

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: us-central1
  REPOSITORY: safehaven-repo
  SERVICE_NAME: safehaven-dashboard

jobs:
  # Security and code quality checks
  security-scan:
    name: Security & Quality Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint --if-present

    - name: Run security audit
      run: npm audit --audit-level=high

  # Unit and integration tests
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    if: ${{ !inputs.skip_tests }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build CSS
      run: npm run build:css-prod

    - name: Run unit tests
      run: npm test --if-present

    - name: Run integration tests
      run: npm run test:integration --if-present

  # Docker build and deploy to Cloud Run
  deploy-docker:
    name: Deploy to Google Cloud Run
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Artifact Registry
      run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev

    - name: Build and deploy with Cloud Build
      run: |
        gcloud builds submit --config cloudbuild.yaml .

    - name: Verify deployment
      run: |
        echo "Waiting for deployment to be ready..."
        sleep 30

        # Get the service URL
        SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format='value(status.url)')

        echo "Service URL: $SERVICE_URL"

        # Health check
        curl -f "$SERVICE_URL/health" || exit 1

        echo "✅ Deployment successful and healthy!"

    - name: Create deployment summary
      run: |
        echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Service:** ${{ env.SERVICE_NAME }}" >> $GITHUB_STEP_SUMMARY
        echo "**Region:** ${{ env.REGION }}" >> $GITHUB_STEP_SUMMARY
        echo "**Build ID:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "**Deployed at:** $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format='value(status.url)')

        echo "**Service URL:** [$SERVICE_URL]($SERVICE_URL)" >> $GITHUB_STEP_SUMMARY

  # Rollback capability
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() && github.ref == 'refs/heads/main'
    needs: [deploy-docker]

    steps:
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Rollback to previous revision
      run: |
        echo "🔄 Rolling back to previous revision..."

        # Get the previous revision
        PREVIOUS_REVISION=$(gcloud run revisions list \
          --service=${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --limit=2 \
          --format='value(metadata.name)' | tail -n 1)

        if [ -n "$PREVIOUS_REVISION" ]; then
          echo "Rolling back to revision: $PREVIOUS_REVISION"

          gcloud run services update-traffic ${{ env.SERVICE_NAME }} \
            --to-revisions=$PREVIOUS_REVISION=100 \
            --region=${{ env.REGION }}

          echo "✅ Rollback completed successfully!"
        else
          echo "❌ No previous revision found for rollback"
          exit 1
        fi

  # Notification job
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    if: always()
    needs: [deploy-docker]

    steps:
    - name: Send Slack notification
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#emergency-alerts'
        text: |
          SafeHaven Dashboard Deployment ${{ job.status }}

          Branch: ${{ github.ref }}
          Commit: ${{ github.sha }}
          Author: ${{ github.actor }}

          ${{ job.status == 'success' && '✅ Deployment successful!' || '❌ Deployment failed!' }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
