name: SafeHaven Emergency Dashboard - CI/CD Pipeline

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      skip_tests:
        description: 'Skip tests'
        required: false
        default: false
        type: boolean

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: us-central1
  REPOSITORY: safehaven-repo
  SERVICE_NAME: safehaven-dashboard

jobs:
  # Security and code quality checks
  security-scan:
    name: Security & Quality Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint --if-present

    - name: Run security audit
      run: npm audit --audit-level=high

    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

    - name: Upload security scan results
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: snyk.sarif
      continue-on-error: true

  # Unit and integration tests
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    if: ${{ !inputs.skip_tests }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build CSS
      run: npm run build:css-prod

    - name: Run unit tests
      run: npm test --if-present

    - name: Run integration tests
      run: npm run test:integration --if-present

    - name: Generate test coverage
      run: npm run test:coverage --if-present

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      if: always()
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        files: ./coverage/lcov.info
        fail_ci_if_error: false

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: |
          coverage/
          test-results.xml

  # Build Docker image
  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop' || github.event_name == 'workflow_dispatch'

    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-url: ${{ steps.build.outputs.image-url }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Artifact Registry
      run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev

    - name: Set build variables
      id: vars
      run: |
        if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          echo "environment=production" >> $GITHUB_OUTPUT
          echo "image-tag=latest" >> $GITHUB_OUTPUT
        elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
          echo "environment=staging" >> $GITHUB_OUTPUT
          echo "image-tag=staging" >> $GITHUB_OUTPUT
        else
          echo "environment=development" >> $GITHUB_OUTPUT
          echo "image-tag=dev-${{ github.sha }}" >> $GITHUB_OUTPUT
        fi

    - name: Build and push Docker image
      id: build
      run: |
        IMAGE_URL="${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/safehaven-dashboard:${{ steps.vars.outputs.image-tag }}"

        gcloud builds submit \
          --config cloudbuild.yaml \
          --substitutions=_IMAGE_TAG=${{ steps.vars.outputs.image-tag }},_ENVIRONMENT=${{ steps.vars.outputs.environment }} \
          .

        echo "image-url=$IMAGE_URL" >> $GITHUB_OUTPUT

        # Get image digest for security
        DIGEST=$(gcloud artifacts docker images describe "$IMAGE_URL" --format='value(image_summary.digest)')
        echo "digest=$DIGEST" >> $GITHUB_OUTPUT

  # Deploy to Cloud Run
  deploy:
    name: Deploy to Cloud Run
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Deploy to Cloud Run
      run: |
        # Determine environment
        if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          ENVIRONMENT="production"
          SERVICE_NAME="${{ env.SERVICE_NAME }}"
        else
          ENVIRONMENT="staging"
          SERVICE_NAME="${{ env.SERVICE_NAME }}-staging"
        fi

        # Deploy using the deployment script
        chmod +x deploy_cloud_run.sh
        ENVIRONMENT="$ENVIRONMENT" SERVICE_NAME="$SERVICE_NAME" IMAGE_TAG="${{ needs.build.outputs.image-tag }}" ./deploy_cloud_run.sh

    - name: Verify deployment
      run: |
        # Determine service name
        if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          SERVICE_NAME="${{ env.SERVICE_NAME }}"
        else
          SERVICE_NAME="${{ env.SERVICE_NAME }}-staging"
        fi

        echo "Waiting for deployment to be ready..."
        sleep 30

        # Get the service URL
        SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" \
          --region=${{ env.REGION }} \
          --format='value(status.url)')

        echo "Service URL: $SERVICE_URL"

        # Health check with retry
        for i in {1..5}; do
          if curl -f -s "$SERVICE_URL/health" > /dev/null; then
            echo "✅ Health check passed on attempt $i"
            break
          else
            echo "❌ Health check failed on attempt $i"
            if [ $i -eq 5 ]; then
              echo "❌ All health checks failed"
              exit 1
            fi
            sleep 10
          fi
        done

        echo "✅ Deployment successful and healthy!"

    - name: Create deployment summary
      run: |
        # Determine service name
        if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          SERVICE_NAME="${{ env.SERVICE_NAME }}"
          ENVIRONMENT="Production"
        else
          SERVICE_NAME="${{ env.SERVICE_NAME }}-staging"
          ENVIRONMENT="Staging"
        fi

        echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** $ENVIRONMENT" >> $GITHUB_STEP_SUMMARY
        echo "**Service:** $SERVICE_NAME" >> $GITHUB_STEP_SUMMARY
        echo "**Region:** ${{ env.REGION }}" >> $GITHUB_STEP_SUMMARY
        echo "**Image:** ${{ needs.build.outputs.image-url }}" >> $GITHUB_STEP_SUMMARY
        echo "**Digest:** ${{ needs.build.outputs.image-digest }}" >> $GITHUB_STEP_SUMMARY
        echo "**Build ID:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "**Deployed at:** $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" \
          --region=${{ env.REGION }} \
          --format='value(status.url)')

        echo "**Service URL:** [$SERVICE_URL]($SERVICE_URL)" >> $GITHUB_STEP_SUMMARY

  # Rollback capability
  rollback:
    name: Rollback Deployment
    runs-on: ubuntu-latest
    if: failure() && github.ref == 'refs/heads/main'
    needs: [deploy]

    steps:
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Rollback to previous revision
      run: |
        echo "🔄 Rolling back to previous revision..."

        # Get the previous revision
        PREVIOUS_REVISION=$(gcloud run revisions list \
          --service=${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --limit=2 \
          --format='value(metadata.name)' | tail -n 1)

        if [ -n "$PREVIOUS_REVISION" ]; then
          echo "Rolling back to revision: $PREVIOUS_REVISION"

          gcloud run services update-traffic ${{ env.SERVICE_NAME }} \
            --to-revisions=$PREVIOUS_REVISION=100 \
            --region=${{ env.REGION }}

          echo "✅ Rollback completed successfully!"
        else
          echo "❌ No previous revision found for rollback"
          exit 1
        fi

  # Notification job
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    if: always()
    needs: [deploy]

    steps:
    - name: Determine deployment status
      id: status
      run: |
        if [[ "${{ needs.deploy.result }}" == "success" ]]; then
          echo "status=success" >> $GITHUB_OUTPUT
          echo "message=✅ Deployment successful!" >> $GITHUB_OUTPUT
        else
          echo "status=failure" >> $GITHUB_OUTPUT
          echo "message=❌ Deployment failed!" >> $GITHUB_OUTPUT
        fi

    - name: Send Slack notification
      if: always() && vars.SLACK_WEBHOOK_URL != ''
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ steps.status.outputs.status }}
        channel: '#emergency-alerts'
        text: |
          SafeHaven Dashboard Deployment ${{ steps.status.outputs.status }}

          Branch: ${{ github.ref }}
          Commit: ${{ github.sha }}
          Author: ${{ github.actor }}

          ${{ steps.status.outputs.message }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Create GitHub issue on failure
      if: failure() && github.ref == 'refs/heads/main'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: '🚨 Production Deployment Failed',
            body: `
              ## Deployment Failure Report

              **Branch:** ${context.ref}
              **Commit:** ${context.sha}
              **Author:** ${context.actor}
              **Workflow:** ${context.workflow}
              **Run ID:** ${context.runId}

              **Action Required:**
              1. Check the deployment logs
              2. Verify the application health
              3. Consider rolling back if necessary

              [View Workflow Run](${context.payload.repository.html_url}/actions/runs/${context.runId})
            `,
            labels: ['bug', 'deployment', 'urgent']
          })
