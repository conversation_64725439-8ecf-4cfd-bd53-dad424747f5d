import React from 'react';
import {
  <PERSON><PERSON>hart3,
  Alert<PERSON><PERSON>gle,
  AlertCircle,
  FileText,
  Home,
  Map,
  Plus,
  Shield,
  User,
  Circle
} from 'lucide-react';
import { UserRole } from '../../src/config/firebase';

const Sidebar = ({ currentPage, onNavigate, isAdmin, userProfile }) => {

  const allMenuItems = [
    {
      page: 'dashboard',
      icon: BarChart3,
      label: 'Dashboard',
      description: 'Overview & Statistics',
      roles: [UserRole.ADMIN, UserRole.USER]
    },
    {
      page: 'alerts',
      icon: AlertTriangle,
      label: 'Active Alerts',
      description: 'View Disaster Alerts',
      roles: [UserRole.ADMIN, UserRole.USER],
      badge: 3 // Example active alerts count
    },
    {
      page: 'sos-messages',
      icon: AlertCircle,
      label: 'SOS Messages',
      description: 'Emergency Requests',
      roles: [UserRole.ADMIN, UserRole.USER],
      badge: 7 // Example SOS count
    },
    {
      page: 'reports',
      icon: FileText,
      label: 'Reports',
      description: 'Incident Reports',
      roles: [UserRole.ADMIN, UserRole.USER]
    },
    {
      page: 'shelters',
      icon: Home,
      label: 'Shelters',
      description: 'Emergency Shelters',
      roles: [UserRole.ADMIN, UserRole.USER]
    },
    {
      page: 'maps',
      icon: Map,
      label: 'Maps',
      description: 'Emergency Response Map',
      roles: [UserRole.ADMIN, UserRole.USER]
    },
    {
      page: 'create-alert',
      icon: Plus,
      label: 'Create Alert',
      description: 'New Emergency Alert',
      roles: [UserRole.ADMIN] // Admin only
    }
  ];

  // Filter menu items based on user role
  const menuItems = allMenuItems.filter(item =>
    item.roles.includes(userProfile?.role || UserRole.USER)
  );

  return (
    <div className="w-64 bg-white shadow-lg h-full flex flex-col border-r border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-sm">
            <Shield className="w-7 h-7 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">SafeHaven</h1>
            <p className="text-sm text-gray-600 font-medium">Emergency Management</p>
          </div>
        </div>

        {/* User Role Badge */}
        <div className="mt-4">
          <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-semibold ${
            isAdmin
              ? 'bg-red-50 text-red-700 border border-red-200'
              : 'bg-blue-50 text-blue-700 border border-blue-200'
          }`}>
            {isAdmin ? (
              <>
                <Shield className="w-4 h-4" />
                <span>Emergency Coordinator</span>
              </>
            ) : (
              <>
                <User className="w-4 h-4" />
                <span>Emergency Observer</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {menuItems.map((item) => {
          const IconComponent = item.icon;
          const isActive = currentPage === item.page;

          return (
            <button
              key={item.page}
              onClick={() => onNavigate(item.page)}
              className={`w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-all duration-200 group ${
                isActive
                  ? 'bg-blue-50 text-blue-700 shadow-sm border-l-4 border-blue-600'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className={`flex-shrink-0 ${isActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'}`}>
                  <IconComponent className="w-5 h-5" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{item.label}</p>
                  <p className={`text-xs truncate ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                    {item.description}
                  </p>
                </div>
              </div>

              {/* Badge for notifications */}
              {item.badge && (
                <div className="flex-shrink-0 ml-2">
                  <span className={`inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded-full ${
                    item.page === 'sos-messages'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-orange-100 text-orange-800'
                  }`}>
                    {item.badge}
                  </span>
                </div>
              )}
            </button>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 flex-shrink-0 space-y-3">
        {/* System Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Circle className="w-2 h-2 text-green-500 fill-current animate-pulse" />
            <span className="text-sm font-medium text-gray-700">System Online</span>
          </div>
          <div className="text-xs text-gray-500">
            {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>

        {/* User Info */}
        {userProfile && (
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full">
              <User className="w-4 h-4 text-blue-700" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {userProfile.displayName || userProfile.email || 'User'}
              </p>
              <p className="text-xs text-gray-500">
                {userProfile.email && userProfile.displayName ? userProfile.email : 'Emergency Personnel'}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
