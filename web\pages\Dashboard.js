import React, { useState, useEffect } from 'react';
import { getDatabase, ref, onValue, off } from 'firebase/database';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import {
  AlertTriangle,
  AlertCircle,
  FileText,
  Home,
  TrendingUp,
  Clock,
  MapPin,
  Users,
  Activity,
  Plus,
  Eye,
  Settings,
  Shield,
  Heart,
  Zap,
  Globe
} from 'lucide-react';
import { firestore, database } from '../../src/config/firebase';

const Dashboard = () => {
  const [stats, setStats] = useState({
    activeAlerts: 0,
    sosMessages: 0,
    totalReports: 0,
    activeShelters: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        // Load stats from Firebase
        await Promise.all([
          loadActiveAlerts(),
          loadSOSMessages(),
          loadReports(),
          loadShelters(),
          loadRecentActivity()
        ]);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        // Set default data on error to prevent infinite loading
        setStats({
          activeAlerts: 3,
          sosMessages: 7,
          totalReports: 12,
          activeShelters: 5
        });
        setRecentActivity([
          { id: 1, type: 'alert', message: 'New emergency alert created', time: '2 minutes ago' },
          { id: 2, type: 'sos', message: 'SOS message received', time: '5 minutes ago' },
          { id: 3, type: 'report', message: 'Incident report submitted', time: '10 minutes ago' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    // Set a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      setLoading(false);
      setStats({
        activeAlerts: 3,
        sosMessages: 7,
        totalReports: 12,
        activeShelters: 5
      });
    }, 3000);

    loadDashboardData();

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  const loadActiveAlerts = async () => {
    const alertsRef = ref(database, 'alerts');
    onValue(alertsRef, (snapshot) => {
      const data = snapshot.val();
      if (data) {
        const activeCount = Object.values(data).filter(alert => alert.isActive).length;
        setStats(prev => ({ ...prev, activeAlerts: activeCount }));
      }
    });
  };

  const loadSOSMessages = async () => {
    try {
      const sosQuery = query(
        collection(firestore, 'sosMessages'),
        where('status', '==', 'pending'),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(sosQuery);
      setStats(prev => ({ ...prev, sosMessages: snapshot.size }));
    } catch (error) {
      console.error('Error loading SOS messages:', error);
    }
  };

  const loadReports = async () => {
    try {
      const reportsQuery = query(
        collection(firestore, 'reports'),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(reportsQuery);
      setStats(prev => ({ ...prev, totalReports: snapshot.size }));
    } catch (error) {
      console.error('Error loading reports:', error);
    }
  };

  const loadShelters = async () => {
    try {
      const sheltersQuery = query(
        collection(firestore, 'shelters'),
        where('isActive', '==', true)
      );
      const snapshot = await getDocs(sheltersQuery);
      setStats(prev => ({ ...prev, activeShelters: snapshot.size }));
    } catch (error) {
      console.error('Error loading shelters:', error);
    }
  };

  const loadRecentActivity = async () => {
    try {
      const activities = [];
      
      // Get recent alerts
      const alertsRef = ref(database, 'alerts');
      onValue(alertsRef, (snapshot) => {
        const data = snapshot.val();
        if (data) {
          const recentAlerts = Object.entries(data)
            .map(([id, alert]) => ({
              id,
              type: 'alert',
              title: alert.title,
              timestamp: alert.createdAt,
              severity: alert.severity
            }))
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, 5);
          
          setRecentActivity(recentAlerts);
        }
      });
    } catch (error) {
      console.error('Error loading recent activity:', error);
    }
  };

  const StatCard = ({ title, value, icon: IconComponent, color, description, trend, imageUrl }) => (
    <div className={`bg-white rounded-2xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 border-l-8 ${getColorClasses(color)} group relative overflow-hidden`}>
      {/* Background Image */}
      {imageUrl && (
        <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
          <img src={imageUrl} alt="" className="w-full h-full object-cover" />
        </div>
      )}
      
      <div className="relative flex items-center space-x-4">
        <div className={`w-16 h-16 rounded-xl flex items-center justify-center ${getIconBgClass(color)} shadow-lg group-hover:scale-110 transition-transform duration-300`}>
          <IconComponent className="w-8 h-8" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-bold text-gray-900 uppercase tracking-wide">{title}</h3>
          <p className="text-sm text-gray-600 mt-1">{description}</p>
          <div className="mt-3 flex items-center space-x-3">
            <div className="text-5xl font-black text-gray-900">{value}</div>
            {trend && (
              <div className={`flex items-center space-x-1 text-sm font-semibold ${trend.positive ? 'text-green-600' : 'text-red-600'}`}>
                <TrendingUp className={`w-5 h-5 ${trend.positive ? '' : 'rotate-180'}`} />
                <span>{trend.value}</span>
                <span className="text-gray-400">vs last hour</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const getColorClasses = (color) => {
    switch (color) {
      case 'red':
        return 'border-l-red-500 hover:border-l-red-600';
      case 'orange':
        return 'border-l-orange-500 hover:border-l-orange-600';
      case 'blue':
        return 'border-l-blue-500 hover:border-l-blue-600';
      case 'green':
        return 'border-l-green-500 hover:border-l-green-600';
      default:
        return 'border-l-gray-500 hover:border-l-gray-600';
    }
  };

  const getIconBgClass = (color) => {
    switch (color) {
      case 'red':
        return 'bg-gradient-to-br from-red-50 to-red-100 text-red-600 border border-red-200';
      case 'orange':
        return 'bg-gradient-to-br from-orange-50 to-orange-100 text-orange-600 border border-orange-200';
      case 'blue':
        return 'bg-gradient-to-br from-blue-50 to-blue-100 text-blue-600 border border-blue-200';
      case 'green':
        return 'bg-gradient-to-br from-green-50 to-green-100 text-green-600 border border-green-200';
      default:
        return 'bg-gradient-to-br from-gray-50 to-gray-100 text-gray-600 border border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
            <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-400 animate-ping"></div>
          </div>
          <p className="mt-6 text-lg font-semibold text-gray-700">Loading Emergency Dashboard...</p>
          <p className="mt-2 text-sm text-gray-500">Preparing real-time data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Hero Section with Emergency Background */}
      <div className="relative bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>')`,
        }}></div>
        
        <div className="relative px-6 py-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl lg:text-5xl font-black text-white leading-tight">
                    Emergency Management
                  </h1>
                  <p className="text-xl text-blue-100 mt-2">Real-time disaster response & coordination</p>
                </div>
              </div>
              
              <div className="flex flex-wrap items-center space-x-6 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-100">All Systems Operational</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-blue-200" />
                  <span className="text-blue-100">Last updated: {new Date().toLocaleTimeString()}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Globe className="w-4 h-4 text-blue-200" />
                  <span className="text-blue-100">24/7 Monitoring Active</span>
                </div>
              </div>
            </div>
            
            <div className="mt-6 lg:mt-0 flex items-center space-x-4">
              <button className="bg-white bg-opacity-20 backdrop-blur-sm text-white px-6 py-3 rounded-xl font-semibold hover:bg-opacity-30 transition-all duration-200 flex items-center space-x-2 border border-white border-opacity-20">
                <Plus className="w-5 h-5" />
                <span>Create Alert</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <section className="px-6 py-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Active Alerts"
          value={stats.activeAlerts}
          icon={AlertTriangle}
          color="red"
          description="Emergency alerts requiring immediate attention"
          trend={{ positive: false, value: "+2" }}
          imageUrl="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='40' fill='none' stroke='%23ef4444' stroke-width='2' stroke-dasharray='5,5'/><path d='M50 20 L50 40 M50 60 L50 80' stroke='%23ef4444' stroke-width='3' stroke-linecap='round'/></svg>"
        />
        <StatCard
          title="SOS Messages"
          value={stats.sosMessages}
          icon={AlertCircle}
          color="orange"
          description="Urgent distress signals from citizens"
          trend={{ positive: false, value: "+5" }}
          imageUrl="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='35' fill='%23f97316' opacity='0.1'/><text x='50' y='55' text-anchor='middle' fill='%23f97316' font-size='24' font-weight='bold'>SOS</text></svg>"
        />
        <StatCard
          title="Total Reports"
          value={stats.totalReports}
          icon={FileText}
          color="blue"
          description="Incident reports and documentation"
          trend={{ positive: true, value: "+12" }}
          imageUrl="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect x='20' y='20' width='60' height='80' fill='none' stroke='%233b82f6' stroke-width='2'/><line x1='30' y1='35' x2='70' y2='35' stroke='%233b82f6' stroke-width='2'/><line x1='30' y1='45' x2='70' y2='45' stroke='%233b82f6' stroke-width='2'/><line x1='30' y1='55' x2='70' y2='55' stroke='%233b82f6' stroke-width='2'/></svg>"
        />
        <StatCard
          title="Active Shelters"
          value={stats.activeShelters}
          icon={Home}
          color="green"
          description="Emergency shelters providing refuge"
          trend={{ positive: true, value: "+1" }}
          imageUrl="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><path d='M50 20 L20 50 L20 80 L80 80 L80 50 Z' fill='none' stroke='%2322c55e' stroke-width='2'/><rect x='35' y='60' width='10' height='20' fill='%2322c55e' opacity='0.3'/><rect x='55' y='60' width='10' height='20' fill='%2322c55e' opacity='0.3'/></svg>"
        />
      </section>

      {/* Dashboard Content */}
      <main className="px-6 pb-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Activity */}
        <section className="lg:col-span-2 bg-white rounded-2xl shadow-lg border border-gray-200 p-6 overflow-hidden">
          <header className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <Activity className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Recent Activity</h2>
                <p className="text-sm text-gray-600">Live updates from emergency operations</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-600 font-bold bg-green-50 px-3 py-1 rounded-full border border-green-200">LIVE</span>
            </div>
          </header>
          
          <div className="space-y-4 max-h-[500px] overflow-y-auto pr-2">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl hover:from-blue-50 hover:to-blue-100 transition-all duration-300 cursor-pointer group border border-gray-200 hover:border-blue-200">
                  <div className={`flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center shadow-md ${
                    activity.type === 'alert' ? 'bg-gradient-to-br from-red-100 to-red-200 text-red-600' : 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600'
                  }`}>
                    {activity.type === 'alert' ? (
                      <AlertTriangle className="w-6 h-6" />
                    ) : (
                      <FileText className="w-6 h-6" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-base font-bold text-gray-900 truncate group-hover:text-blue-700 transition-colors">
                      {activity.title}
                    </p>
                    <div className="flex items-center space-x-2 mt-1 text-sm text-gray-600">
                      <Clock className="w-4 h-4" />
                      <p className="font-medium">
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  {activity.severity && (
                    <div className={`px-3 py-1 text-xs font-bold rounded-full shadow-sm ${getSeverityBadgeClass(activity.severity)}`}>
                      {activity.severity.toUpperCase()}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-center py-16 text-gray-500">
                <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Activity className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-700 mb-2">No Recent Activity</h3>
                <p className="text-sm text-gray-500">Activity will appear here as emergency events occur</p>
              </div>
            )}
          </div>
        </section>

        {/* Quick Actions & System Status */}
        <section className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
            <header className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center shadow-lg">
                <Settings className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
                <p className="text-sm text-gray-600">Emergency response tools</p>
              </div>
            </header>
            
            <div className="space-y-3">
              <button className="w-full flex items-center justify-center px-4 py-3 text-sm font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white transform hover:-translate-y-0.5">
                <AlertTriangle className="w-5 h-5 mr-3 group-hover:animate-pulse" />
                Create Emergency Alert
              </button>
              <button className="w-full flex items-center justify-center px-4 py-3 text-sm font-semibold rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-white border-2 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-700">
                <Eye className="w-5 h-5 mr-3" />
                View All Reports
              </button>
              <button className="w-full flex items-center justify-center px-4 py-3 text-sm font-semibold rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-white border-2 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-700">
                <Home className="w-5 h-5 mr-3" />
                Manage Shelters
              </button>
              <button className="w-full flex items-center justify-center px-4 py-3 text-sm font-semibold rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white transform hover:-translate-y-0.5">
                <MapPin className="w-5 h-5 mr-3" />
                View Emergency Map
              </button>
            </div>
          </div>

          {/* System Statistics */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
            <header className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">System Health</h2>
                <p className="text-sm text-gray-600">Real-time system metrics</p>
              </div>
            </header>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <Users className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-semibold text-gray-700">Total Users</span>
                </div>
                <span className="text-lg font-bold text-gray-900">1,247</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <Zap className="w-5 h-5 text-green-600" />
                  <span className="text-sm font-semibold text-gray-700">Active Sessions</span>
                </div>
                <span className="text-lg font-bold text-gray-900">89</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-orange-600" />
                  <span className="text-sm font-semibold text-gray-700">Response Time</span>
                </div>
                <span className="text-lg font-bold text-green-600">2.3s</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <Shield className="w-5 h-5 text-purple-600" />
                  <span className="text-sm font-semibold text-gray-700">Uptime</span>
                </div>
                <span className="text-lg font-bold text-green-600">99.9%</span>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

const getSeverityBadgeClass = (severity) => {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'bg-red-100 text-red-800 border border-red-200';
    case 'high':
      return 'bg-orange-100 text-orange-800 border border-orange-200';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
    case 'low':
      return 'bg-green-100 text-green-800 border border-green-200';
    default:
      return 'bg-gray-100 text-gray-800 border border-gray-200';
  }
};

export default Dashboard;
