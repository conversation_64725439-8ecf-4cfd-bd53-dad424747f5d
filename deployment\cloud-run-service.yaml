# SafeHaven Emergency Management Dashboard
# Google Cloud Run Service Configuration

apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: safehaven-dashboard
  namespace: default
  labels:
    app: safehaven
    component: dashboard
    environment: production
    version: "1.0.0"
  annotations:
    # Cloud Run specific annotations
    run.googleapis.com/ingress: all
    run.googleapis.com/ingress-status: all
    # Custom domain mapping
    run.googleapis.com/custom-audiences: "safehaven-dashboard"
    # Binary authorization
    run.googleapis.com/binary-authorization-breakglass: "false"
    # VPC connector for private resources
    run.googleapis.com/vpc-access-connector: "safehaven-connector"
    run.googleapis.com/vpc-access-egress: "private-ranges-only"
spec:
  template:
    metadata:
      labels:
        app: safehaven
        component: dashboard
        environment: production
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "100"
        autoscaling.knative.dev/target: "80"
        # Resource allocation
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
        # Execution environment
        run.googleapis.com/execution-environment: gen2
        # Startup probe
        run.googleapis.com/startup-cpu-boost: "true"
        # Session affinity for emergency operations
        run.googleapis.com/sessionAffinity: "true"
    spec:
      # Service account for Firebase and GCP services
      serviceAccountName: <EMAIL>
      
      # Container timeout for emergency operations
      timeoutSeconds: 300
      
      # Container configuration
      containers:
      - name: safehaven-dashboard
        # Image will be set by CI/CD pipeline
        image: gcr.io/safehaven-463909/safehaven-dashboard:latest
        
        # Resource limits for emergency management workloads
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
          requests:
            cpu: "1"
            memory: "1Gi"
        
        # Container port
        ports:
        - name: http1
          containerPort: 8080
          protocol: TCP
        
        # Environment variables
        env:
        # Firebase Configuration
        - name: EXPO_PUBLIC_FIREBASE_API_KEY
          valueFrom:
            secretKeyRef:
              name: firebase-config
              key: api-key
        - name: EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN
          valueFrom:
            secretKeyRef:
              name: firebase-config
              key: auth-domain
        - name: EXPO_PUBLIC_FIREBASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: firebase-config
              key: project-id
        - name: EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET
          valueFrom:
            secretKeyRef:
              name: firebase-config
              key: storage-bucket
        - name: EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
          valueFrom:
            secretKeyRef:
              name: firebase-config
              key: messaging-sender-id
        - name: EXPO_PUBLIC_FIREBASE_APP_ID
          valueFrom:
            secretKeyRef:
              name: firebase-config
              key: app-id
        - name: EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID
          valueFrom:
            secretKeyRef:
              name: firebase-config
              key: measurement-id
        - name: EXPO_PUBLIC_FIREBASE_DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: firebase-config
              key: database-url
        
        # Google Maps Configuration
        - name: EXPO_PUBLIC_GOOGLE_MAPS_API_KEY
          valueFrom:
            secretKeyRef:
              name: google-maps-config
              key: api-key
        
        # Twilio Configuration
        - name: EXPO_PUBLIC_TWILIO_ACCOUNT_SID
          valueFrom:
            secretKeyRef:
              name: twilio-config
              key: account-sid
        - name: EXPO_PUBLIC_TWILIO_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: twilio-config
              key: auth-token
        - name: EXPO_PUBLIC_TWILIO_PHONE_NUMBER
          valueFrom:
            secretKeyRef:
              name: twilio-config
              key: phone-number
        
        # Application Configuration
        - name: EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM
          value: "5"
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8080"
        
        # Health check configuration
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        startupProbe:
          httpGet:
            path: /health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 30
          successThreshold: 1

  # Traffic configuration
  traffic:
  - percent: 100
    latestRevision: true

---
# IAM Policy for Cloud Run Service
apiVersion: v1
kind: ConfigMap
metadata:
  name: safehaven-iam-policy
  namespace: default
data:
  policy.json: |
    {
      "bindings": [
        {
          "role": "roles/run.invoker",
          "members": [
            "allUsers"
          ]
        },
        {
          "role": "roles/run.developer",
          "members": [
            "serviceAccount:<EMAIL>",
            "group:<EMAIL>"
          ]
        }
      ]
    }

---
# Custom Domain Mapping
apiVersion: serving.knative.dev/v1alpha1
kind: DomainMapping
metadata:
  name: dashboard.safehaven.org
  namespace: default
  labels:
    app: safehaven
    component: dashboard
spec:
  ref:
    name: safehaven-dashboard
    kind: Service
    apiVersion: serving.knative.dev/v1
  # SSL certificate will be automatically provisioned
  certificateMode: AUTOMATIC

---
# Staging Environment Service
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: safehaven-dashboard-staging
  namespace: default
  labels:
    app: safehaven
    component: dashboard
    environment: staging
spec:
  template:
    metadata:
      labels:
        app: safehaven
        component: dashboard
        environment: staging
      annotations:
        # Reduced resources for staging
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/memory: "1Gi"
        run.googleapis.com/cpu: "1"
        run.googleapis.com/execution-environment: gen2
    spec:
      serviceAccountName: <EMAIL>
      timeoutSeconds: 300
      containers:
      - name: safehaven-dashboard-staging
        image: gcr.io/safehaven-463909/safehaven-dashboard:staging
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "0.5"
            memory: "512Mi"
        ports:
        - name: http1
          containerPort: 8080
        env:
        # Use staging Firebase project
        - name: EXPO_PUBLIC_FIREBASE_PROJECT_ID
          value: "safehaven-staging"
        # Other environment variables same as production
        - name: NODE_ENV
          value: "staging"
        - name: PORT
          value: "8080"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10

---
# Staging Domain Mapping
apiVersion: serving.knative.dev/v1alpha1
kind: DomainMapping
metadata:
  name: staging.safehaven.org
  namespace: default
spec:
  ref:
    name: safehaven-dashboard-staging
    kind: Service
    apiVersion: serving.knative.dev/v1
  certificateMode: AUTOMATIC
