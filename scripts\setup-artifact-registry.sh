#!/bin/bash

# SafeHaven Emergency Management Dashboard
# Google Cloud Artifact Registry Setup Script
# This script sets up the Artifact Registry repository and configures authentication

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-safehaven-463909}"
REGION="${REGION:-us-central1}"
REPOSITORY_NAME="${REPOSITORY_NAME:-safehaven-repo}"
SERVICE_ACCOUNT_NAME="${SERVICE_ACCOUNT_NAME:-safehaven-dashboard}"
DESCRIPTION="SafeHaven Emergency Management Dashboard Docker Repository"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if gcloud is installed and authenticated
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "No active gcloud authentication found. Please run 'gcloud auth login'"
        exit 1
    fi
    
    # Set the project
    gcloud config set project "$PROJECT_ID"
    log_success "Prerequisites check completed"
}

# Enable required APIs
enable_apis() {
    log_info "Enabling required Google Cloud APIs..."
    
    local apis=(
        "artifactregistry.googleapis.com"
        "cloudbuild.googleapis.com"
        "run.googleapis.com"
        "secretmanager.googleapis.com"
        "iam.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        log_info "Enabling $api..."
        gcloud services enable "$api" --project="$PROJECT_ID"
    done
    
    log_success "All required APIs enabled"
}

# Create Artifact Registry repository
create_repository() {
    log_info "Creating Artifact Registry repository..."
    
    # Check if repository already exists
    if gcloud artifacts repositories describe "$REPOSITORY_NAME" \
        --location="$REGION" \
        --project="$PROJECT_ID" &>/dev/null; then
        log_warning "Repository $REPOSITORY_NAME already exists"
        return 0
    fi
    
    # Create the repository
    gcloud artifacts repositories create "$REPOSITORY_NAME" \
        --repository-format=docker \
        --location="$REGION" \
        --description="$DESCRIPTION" \
        --project="$PROJECT_ID"
    
    log_success "Artifact Registry repository created: $REPOSITORY_NAME"
}

# Create service account for Cloud Run
create_service_account() {
    log_info "Creating service account for Cloud Run..."
    
    local sa_email="${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    # Check if service account already exists
    if gcloud iam service-accounts describe "$sa_email" \
        --project="$PROJECT_ID" &>/dev/null; then
        log_warning "Service account $sa_email already exists"
    else
        # Create service account
        gcloud iam service-accounts create "$SERVICE_ACCOUNT_NAME" \
            --description="SafeHaven Dashboard Service Account" \
            --display-name="SafeHaven Dashboard" \
            --project="$PROJECT_ID"
        
        log_success "Service account created: $sa_email"
    fi
    
    # Grant necessary permissions
    log_info "Granting IAM permissions..."
    
    local roles=(
        "roles/run.invoker"
        "roles/secretmanager.secretAccessor"
        "roles/artifactregistry.reader"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
        "roles/cloudtrace.agent"
    )
    
    for role in "${roles[@]}"; do
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:$sa_email" \
            --role="$role" \
            --quiet
    done
    
    log_success "IAM permissions granted"
}

# Configure Docker authentication
configure_docker_auth() {
    log_info "Configuring Docker authentication for Artifact Registry..."
    
    # Configure Docker to use gcloud as credential helper
    gcloud auth configure-docker "${REGION}-docker.pkg.dev" --quiet
    
    log_success "Docker authentication configured"
}

# Create GitHub Actions service account and key
create_github_sa() {
    log_info "Creating GitHub Actions service account..."
    
    local github_sa_name="github-actions-sa"
    local github_sa_email="${github_sa_name}@${PROJECT_ID}.iam.gserviceaccount.com"
    local key_file="github-actions-key.json"
    
    # Check if service account already exists
    if gcloud iam service-accounts describe "$github_sa_email" \
        --project="$PROJECT_ID" &>/dev/null; then
        log_warning "GitHub Actions service account already exists"
    else
        # Create service account
        gcloud iam service-accounts create "$github_sa_name" \
            --description="GitHub Actions CI/CD Service Account" \
            --display-name="GitHub Actions" \
            --project="$PROJECT_ID"
        
        log_success "GitHub Actions service account created"
    fi
    
    # Grant necessary permissions for CI/CD
    local github_roles=(
        "roles/cloudbuild.builds.builder"
        "roles/artifactregistry.writer"
        "roles/run.admin"
        "roles/secretmanager.secretAccessor"
        "roles/iam.serviceAccountUser"
    )
    
    for role in "${github_roles[@]}"; do
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:$github_sa_email" \
            --role="$role" \
            --quiet
    done
    
    # Create and download service account key
    if [ ! -f "$key_file" ]; then
        gcloud iam service-accounts keys create "$key_file" \
            --iam-account="$github_sa_email" \
            --project="$PROJECT_ID"
        
        log_success "Service account key created: $key_file"
        log_warning "Please add this key as GCP_SA_KEY secret in your GitHub repository"
        log_warning "Then delete the local key file for security"
    fi
}

# Test the setup
test_setup() {
    log_info "Testing Artifact Registry setup..."
    
    local test_image="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}/test:latest"
    
    # Pull a small test image and tag it
    docker pull hello-world:latest
    docker tag hello-world:latest "$test_image"
    
    # Try to push the test image
    if docker push "$test_image"; then
        log_success "Test push successful"
        
        # Clean up test image
        gcloud artifacts docker images delete "$test_image" \
            --quiet \
            --project="$PROJECT_ID" || true
        docker rmi "$test_image" || true
        docker rmi hello-world:latest || true
    else
        log_error "Test push failed"
        return 1
    fi
}

# Display setup summary
display_summary() {
    log_success "Artifact Registry setup completed successfully!"
    echo
    echo "=== Setup Summary ==="
    echo "Project ID: $PROJECT_ID"
    echo "Region: $REGION"
    echo "Repository: $REPOSITORY_NAME"
    echo "Repository URL: ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}"
    echo "Service Account: ${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
    echo
    echo "=== Next Steps ==="
    echo "1. Add the following secrets to your GitHub repository:"
    echo "   - GCP_PROJECT_ID: $PROJECT_ID"
    echo "   - GCP_SA_KEY: (contents of github-actions-key.json)"
    echo
    echo "2. Update your Docker build commands to use:"
    echo "   docker build -t ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}/safehaven-dashboard:latest ."
    echo
    echo "3. Push images using:"
    echo "   docker push ${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}/safehaven-dashboard:latest"
    echo
}

# Main execution
main() {
    log_info "Starting SafeHaven Artifact Registry setup..."
    
    check_prerequisites
    enable_apis
    create_repository
    create_service_account
    configure_docker_auth
    create_github_sa
    test_setup
    display_summary
    
    log_success "Setup completed successfully!"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
