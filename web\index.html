<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <title>SafeHaven - Emergency Management System</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="/favicon.ico" />
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="./styles.css" />
    
    <style>
      /* Critical CSS for proper layout */
      html, body {
        height: 100%;
        margin: 0;
        padding: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      #root {
        height: 100%;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Loading spinner */
      .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    </style>
  </head>

  <body>
    <noscript>
      <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
        <h1>SafeHaven Emergency Management System</h1>
        <p>You need to enable JavaScript to run this application.</p>
      </div>
    </noscript>
    
    <div id="root">
      <!-- Loading state -->
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- React App Script -->
    <script src="/bundle.js"></script>
  </body>
</html> 