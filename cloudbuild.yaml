# SafeHaven Emergency Management Dashboard
# Google Cloud Build Configuration

steps:
  # Step 1: Fetch secrets from Secret Manager
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'fetch-secrets'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Fetching secrets from Secret Manager..."

        # Firebase secrets
        gcloud secrets versions access latest --secret="firebase-api-key" > /workspace/firebase-api-key.txt
        gcloud secrets versions access latest --secret="firebase-auth-domain" > /workspace/firebase-auth-domain.txt
        gcloud secrets versions access latest --secret="firebase-project-id" > /workspace/firebase-project-id.txt
        gcloud secrets versions access latest --secret="firebase-storage-bucket" > /workspace/firebase-storage-bucket.txt
        gcloud secrets versions access latest --secret="firebase-messaging-sender-id" > /workspace/firebase-messaging-sender-id.txt
        gcloud secrets versions access latest --secret="firebase-app-id" > /workspace/firebase-app-id.txt
        gcloud secrets versions access latest --secret="firebase-measurement-id" > /workspace/firebase-measurement-id.txt
        gcloud secrets versions access latest --secret="firebase-database-url" > /workspace/firebase-database-url.txt

        # Google Maps secret
        gcloud secrets versions access latest --secret="google-maps-api-key" > /workspace/google-maps-api-key.txt

        # Twilio secrets
        gcloud secrets versions access latest --secret="twilio-account-sid" > /workspace/twilio-account-sid.txt
        gcloud secrets versions access latest --secret="twilio-auth-token" > /workspace/twilio-auth-token.txt
        gcloud secrets versions access latest --secret="twilio-phone-number" > /workspace/twilio-phone-number.txt

        echo "Secrets fetched successfully"

  # Step 2: Build Docker image with secrets
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '--target=production'
      - '--build-arg=EXPO_PUBLIC_FIREBASE_API_KEY=$(cat /workspace/firebase-api-key.txt)'
      - '--build-arg=EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=$(cat /workspace/firebase-auth-domain.txt)'
      - '--build-arg=EXPO_PUBLIC_FIREBASE_PROJECT_ID=$(cat /workspace/firebase-project-id.txt)'
      - '--build-arg=EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=$(cat /workspace/firebase-storage-bucket.txt)'
      - '--build-arg=EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=$(cat /workspace/firebase-messaging-sender-id.txt)'
      - '--build-arg=EXPO_PUBLIC_FIREBASE_APP_ID=$(cat /workspace/firebase-app-id.txt)'
      - '--build-arg=EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=$(cat /workspace/firebase-measurement-id.txt)'
      - '--build-arg=EXPO_PUBLIC_FIREBASE_DATABASE_URL=$(cat /workspace/firebase-database-url.txt)'
      - '--build-arg=EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=$(cat /workspace/google-maps-api-key.txt)'
      - '--build-arg=EXPO_PUBLIC_TWILIO_ACCOUNT_SID=$(cat /workspace/twilio-account-sid.txt)'
      - '--build-arg=EXPO_PUBLIC_TWILIO_AUTH_TOKEN=$(cat /workspace/twilio-auth-token.txt)'
      - '--build-arg=EXPO_PUBLIC_TWILIO_PHONE_NUMBER=$(cat /workspace/twilio-phone-number.txt)'
      - '--build-arg=EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=5'
      - '--tag=us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:$BUILD_ID'
      - '--tag=us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:latest'
      - '--cache-from=us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:latest'
      - '.'
    waitFor: ['fetch-secrets']

  # Step 3: Push image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-image'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:$BUILD_ID'
    waitFor: ['build-image']

  # Step 4: Push latest tag
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-latest'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:latest'
    waitFor: ['build-image']

  # Step 5: Deploy to Cloud Run (staging)
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-staging'
    args:
      - 'run'
      - 'deploy'
      - 'safehaven-dashboard-staging'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=1Gi'
      - '--cpu=1'
      - '--min-instances=0'
      - '--max-instances=10'
      - '--timeout=300'
      - '--concurrency=80'
      - '--port=8080'
      - '--set-env-vars=NODE_ENV=staging,EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=5'
    waitFor: ['push-image']

  # Step 6: Deploy to production
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-production'
    args:
      - 'run'
      - 'deploy'
      - 'safehaven-dashboard'
      - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:$BUILD_ID'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=2Gi'
      - '--cpu=2'
      - '--min-instances=1'
      - '--max-instances=100'
      - '--timeout=300'
      - '--concurrency=80'
      - '--port=8080'
      - '--set-env-vars=NODE_ENV=production,EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=5'
      - '--tag=production'
    waitFor: ['deploy-staging']

  # Step 7: Health check production deployment
  - name: 'gcr.io/cloud-builders/curl'
    id: 'health-check'
    args:
      - '-f'
      - 'https://safehaven-dashboard-$PROJECT_ID.a.run.app/health'
    waitFor: ['deploy-production']

# Build artifacts
artifacts:
  images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:$BUILD_ID'
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/safehaven-repo/safehaven-dashboard:latest'

# Build timeout (30 minutes for emergency management builds)
timeout: '1800s'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: '100'
  logging: CLOUD_LOGGING_ONLY

# Substitutions for different environments
substitutions:
  _REGION: 'us-central1'
  _SERVICE_NAME: 'safehaven-dashboard'
  _STAGING_SERVICE_NAME: 'safehaven-dashboard-staging'

# Build tags for organization
tags:
  - 'safehaven'
  - 'dashboard'
  - 'emergency-management'
  - 'production'
