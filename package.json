{"name": "safehaven", "version": "1.0.0", "type": "module", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "dev": "expo start --web", "dev:server": "nodemon server/index.cjs", "build": "npm run build:css-prod && expo export --platform web", "build:web": "npm run build:css-prod && expo export --platform web", "build:android": "expo build:android", "build:ios": "expo build:ios", "build:css": "npx tailwindcss -i ./web/styles.css -o ./dist/styles.css --watch", "build:css-prod": "npx tailwindcss -i ./web/styles.css -o ./dist/styles.css --minify", "prebuild": "npm run build:css-prod", "serve": "serve -s dist -l 8080", "server": "node server/index.cjs", "eject": "expo eject", "lint": "eslint .", "test": "jest", "test:build": "node scripts/test-build.js", "prepare-functions": "cd functions && npm install", "deploy:hosting": "firebase deploy --only hosting", "deploy:functions": "firebase deploy --only functions", "deploy:all": "firebase deploy"}, "dependencies": {"@expo/metro-runtime": "~4.0.1", "@firebasegen/default-connector": "file:dataconnect-generated/js/default-connector", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@react-native-async-storage/async-storage": "^1.24.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "@tailwindcss/forms": "^0.5.10", "axios": "^1.7.9", "body-parser": "^1.20.3", "compression": "^1.7.5", "cors": "^2.8.5", "dotenv": "^16.4.7", "expo": "~52.0.47", "expo-constants": "~17.0.8", "expo-image-picker": "^16.0.6", "expo-location": "^18.0.10", "expo-notifications": "^0.29.14", "expo-status-bar": "~2.0.1", "express": "^4.21.2", "firebase": "^11.6.1", "firebase-admin": "^13.4.0", "google-map-react": "^2.2.1", "helmet": "^8.0.0", "lucide-react": "^0.525.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-dotenv": "^3.4.11", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.25.0", "react-native-maps": "^1.22.6", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-web": "~0.19.13", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.27.7", "@tailwindcss/forms": "^0.5.9", "@types/google-map-react": "^2.1.10", "@types/react": "~18.3.23", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-universe": "^12.1.0", "jest": "^29.7.0", "jest-expo": "^52.0.6", "nodemon": "^3.1.9", "postcss": "^8.5.1", "serve": "^14.2.3", "typescript": "^5.3.3"}, "private": true}