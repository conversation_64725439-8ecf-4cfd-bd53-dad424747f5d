# SafeHaven Emergency Management Dashboard - Complete Docker Deployment Guide

## Overview

This guide provides a complete Docker containerization and deployment pipeline for the SafeHaven disaster alert application. The solution includes multi-stage Docker builds, Google Cloud Artifact Registry, Cloud Run deployment, and comprehensive CI/CD with GitHub Actions.

## 🏗️ Architecture

### Deployment Stack
- **Containerization**: Multi-stage Docker builds with security best practices
- **Registry**: Google Cloud Artifact Registry
- **Hosting**: Google Cloud Run (serverless containers)
- **CI/CD**: GitHub Actions with automated testing and deployment
- **Monitoring**: Cloud Run monitoring + optional Prometheus/Grafana
- **Security**: Google Cloud Secret Manager for environment variables

### Environments
- **Production**: `safehaven-dashboard` (main service)
- **Staging**: `safehaven-dashboard-staging` (testing environment)
- **Development**: Local Docker containers with hot reload

## 🚀 Quick Start

### Prerequisites
- Docker Desktop 4.0+
- Node.js 18+
- Google Cloud SDK
- Git
- Active Google Cloud Project with billing enabled

### 1. <PERSON>lone and Setup
```bash
git clone https://github.com/your-org/SafeHaven.git
cd SafeHaven

# Run the complete setup script
chmod +x scripts/setup-deployment.sh
./scripts/setup-deployment.sh
```

### 2. Local Development
```bash
# Start development environment
docker-compose up safehaven-dev

# Or start production build for testing
docker-compose up safehaven-prod

# With monitoring stack
docker-compose --profile monitoring up
```

### 3. Deploy to Production
```bash
# Manual deployment
chmod +x deploy_cloud_run.sh
./deploy_cloud_run.sh

# Or push to main branch for automated deployment
git push origin main
```

## 📦 Docker Configuration

### Multi-Stage Dockerfile
The Dockerfile includes optimized stages:

1. **Dependencies**: Install and build CSS dependencies
2. **Builder**: Build the React/Expo web application
3. **Production**: Lightweight Node.js server with security hardening
4. **Development**: Hot-reload environment for local development

### Key Features
- ✅ Non-root user execution (UID 1001)
- ✅ Multi-stage builds for minimal image size
- ✅ Security scanning and vulnerability management
- ✅ Health checks and readiness probes
- ✅ Proper signal handling with dumb-init
- ✅ Optimized layer caching

## ☁️ Google Cloud Setup

### Artifact Registry
```bash
# Setup Artifact Registry and permissions
./scripts/setup-artifact-registry.sh
```

Creates:
- Docker repository: `us-central1-docker.pkg.dev/PROJECT_ID/safehaven-repo`
- Service accounts with proper IAM roles
- Docker authentication configuration

### Secret Manager
```bash
# Setup secrets interactively
./scripts/setup-secrets.sh

# Or batch mode for CI/CD
./scripts/setup-secrets.sh batch
```

Manages secrets for:
- Firebase configuration
- Google Maps API key
- Twilio credentials
- Application secrets (JWT, encryption keys)

### Cloud Run Configuration
- **Production**: 2 CPU, 2Gi memory, 1-100 instances
- **Staging**: 1 CPU, 1Gi memory, 0-10 instances
- **Auto-scaling**: Based on CPU and request metrics
- **Health checks**: `/health` and `/readiness` endpoints
- **Security**: Non-root containers, IAM policies

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
The pipeline includes:

1. **Security Scan**: ESLint, npm audit, Snyk security scanning
2. **Testing**: Unit tests, integration tests, coverage reporting
3. **Build**: Multi-environment Docker image builds
4. **Deploy**: Automated Cloud Run deployment
5. **Verify**: Health checks and deployment validation
6. **Rollback**: Automatic rollback on failure
7. **Notify**: Slack notifications and GitHub issue creation

### Environments
- **Main branch** → Production deployment
- **Develop branch** → Staging deployment
- **Pull requests** → Security scan and tests only

### Required GitHub Secrets
```
GCP_PROJECT_ID=your-gcp-project-id
GCP_SA_KEY=<service-account-json-key>
SLACK_WEBHOOK_URL=https://hooks.slack.com/... (optional)
CODECOV_TOKEN=your-codecov-token (optional)
SNYK_TOKEN=your-snyk-token (optional)
```

## 🔧 Configuration Management

### Environment-Specific Configuration
- `config/environments/production.env`
- `config/environments/staging.env`
- `config/environments/development.env`

### Configuration Loader
The `config/config-loader.js` module:
- Loads environment-specific settings
- Manages Google Cloud secrets
- Validates required configuration
- Provides typed configuration access

### Feature Flags
```javascript
const config = require('./config/config-loader');

if (config.getBoolean('FEATURE_REAL_TIME_ALERTS')) {
  // Enable real-time alerts
}
```

## 📊 Monitoring and Logging

### Built-in Monitoring
- **Health Checks**: `/health`, `/readiness`, `/api/health`
- **Metrics**: Request count, response time, error rate
- **Logging**: Structured JSON logs with Cloud Logging
- **Error Reporting**: Automatic error detection and alerting

### Optional Monitoring Stack
```bash
# Start with Prometheus and Grafana
docker-compose --profile monitoring up
```

Access:
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000 (admin/admin123)

### Log Analysis
```bash
# View recent logs
gcloud logs read "resource.type=cloud_run_revision" --limit=50

# Filter error logs
gcloud logs read "severity>=ERROR" --limit=20
```

## 🔒 Security Features

### Container Security
- Non-root user execution (nodejs:1001)
- Read-only root filesystem where possible
- Minimal Alpine Linux base images
- Security context with dropped capabilities
- Regular vulnerability scanning

### Network Security
- HTTPS-only with automatic SSL certificates
- CORS configuration for allowed origins
- Helmet.js security headers
- Rate limiting and request validation

### Secret Management
- Google Cloud Secret Manager integration
- No secrets in container images or code
- Automatic secret rotation support
- Environment-specific secret access

## 🚨 Emergency Procedures

### Immediate Rollback
```bash
# Get previous revision
PREV_REVISION=$(gcloud run revisions list --service=safehaven-dashboard --limit=2 --format='value(metadata.name)' | tail -n 1)

# Rollback traffic
gcloud run services update-traffic safehaven-dashboard --to-revisions=$PREV_REVISION=100
```

### Scale for Emergency
```bash
# Increase capacity immediately
gcloud run services update safehaven-dashboard --min-instances=10 --max-instances=500
```

### Debug Mode
```bash
# Enable debug logging
gcloud run services update safehaven-dashboard --set-env-vars="LOG_LEVEL=debug"
```

## 📋 Maintenance

### Regular Tasks
- **Weekly**: Review logs and metrics
- **Monthly**: Update dependencies and base images
- **Quarterly**: Security audit and penetration testing

### Updates
```bash
# Update dependencies
npm update
npm audit fix

# Rebuild and deploy
docker build -t safehaven-new .
./deploy_cloud_run.sh
```

## 🔍 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker build --no-cache .
```

#### Deployment Issues
```bash
# Check build logs
gcloud builds log BUILD_ID

# Check service logs
gcloud logs read --service=safehaven-dashboard
```

#### Health Check Failures
```bash
# Test health endpoint
curl -f https://your-service-url/health

# Check container status
gcloud run services describe safehaven-dashboard
```

## 📞 Support

### Documentation
- **Deployment Guide**: DEPLOYMENT_GUIDE.md
- **Build Guide**: BUILD_GUIDE.md
- **Component Usage**: COMPONENT_USAGE_GUIDE.md

### Monitoring
- **Service URL**: https://safehaven-dashboard-PROJECT_ID.a.run.app
- **Health Check**: https://safehaven-dashboard-PROJECT_ID.a.run.app/health
- **Metrics**: Google Cloud Console > Cloud Run

### Emergency Contact
- **Technical Issues**: <EMAIL>
- **Critical Emergencies**: <EMAIL>

---

**Last Updated**: December 2024  
**Version**: 2.0.0  
**Maintained by**: SafeHaven DevOps Team
