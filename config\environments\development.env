# SafeHaven Emergency Management Dashboard
# Development Environment Configuration

# Application Settings
NODE_ENV=development
PORT=8080
LOG_LEVEL=debug

# Security Settings
HELMET_ENABLED=false
CORS_ENABLED=true
RATE_LIMITING_ENABLED=false
SESSION_SECURE=false

# Performance Settings
COMPRESSION_ENABLED=false
CACHE_ENABLED=false
CACHE_TTL=60

# Monitoring Settings
METRICS_ENABLED=false
HEALTH_CHECK_ENABLED=true
ERROR_REPORTING_ENABLED=false

# Database Settings
DB_POOL_SIZE=2
DB_TIMEOUT=5000
DB_RETRY_ATTEMPTS=1

# External Services
TWILIO_ENABLED=false
GOOGLE_MAPS_ENABLED=true
FIREBASE_ENABLED=true

# Alert Settings
DEFAULT_ALERT_RADIUS_KM=5
MAX_ALERT_RADIUS_KM=10
ALERT_BATCH_SIZE=10
ALERT_RETRY_ATTEMPTS=1

# File Upload Settings
MAX_FILE_SIZE=1048576
ALLOWED_FILE_TYPES=image/jpeg,image/png

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Session Settings
SESSION_TIMEOUT=3600000
SESSION_CLEANUP_INTERVAL=600000

# Backup Settings
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=1

# Feature Flags
FEATURE_REAL_TIME_ALERTS=true
FEATURE_GEOFENCING=false
FEATURE_MULTI_LANGUAGE=false
FEATURE_DARK_MODE=true
FEATURE_OFFLINE_MODE=false

# Development Specific
HOT_RELOAD_ENABLED=true
DEBUG_MODE=true
MOCK_EXTERNAL_SERVICES=true
VERBOSE_LOGGING=true

# Local Development
LOCAL_FIREBASE_EMULATOR=true
LOCAL_STORAGE_PATH=./tmp/uploads
LOCAL_CACHE_PATH=./tmp/cache
