#!/bin/bash

# SafeHaven Emergency Management Dashboard
# Complete Deployment Setup Script
# This script sets up the entire deployment pipeline from scratch

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-safehaven-463909}"
REGION="${REGION:-us-central1}"
GITHUB_REPO="${GITHUB_REPO:-your-org/SafeHaven}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Display banner
display_banner() {
    echo
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    SafeHaven Deployment Setup               ║"
    echo "║              Emergency Management Dashboard                  ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo
}

# Check prerequisites
check_prerequisites() {
    log_step "Checking prerequisites..."
    
    local missing_tools=()
    
    # Check required tools
    if ! command -v gcloud &> /dev/null; then
        missing_tools+=("gcloud")
    fi
    
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi
    
    if ! command -v git &> /dev/null; then
        missing_tools+=("git")
    fi
    
    if ! command -v node &> /dev/null; then
        missing_tools+=("node")
    fi
    
    if ! command -v npm &> /dev/null; then
        missing_tools+=("npm")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        echo "Please install the missing tools and run this script again."
        exit 1
    fi
    
    # Check gcloud authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "No active gcloud authentication found"
        echo "Please run 'gcloud auth login' and try again."
        exit 1
    fi
    
    # Set the project
    gcloud config set project "$PROJECT_ID"
    
    log_success "Prerequisites check completed"
}

# Setup Google Cloud resources
setup_gcp_resources() {
    log_step "Setting up Google Cloud resources..."
    
    # Run Artifact Registry setup
    if [ -f "scripts/setup-artifact-registry.sh" ]; then
        log_info "Setting up Artifact Registry..."
        chmod +x scripts/setup-artifact-registry.sh
        ./scripts/setup-artifact-registry.sh
    else
        log_warning "Artifact Registry setup script not found"
    fi
    
    # Run secrets setup
    if [ -f "scripts/setup-secrets.sh" ]; then
        log_info "Setting up secrets..."
        chmod +x scripts/setup-secrets.sh
        ./scripts/setup-secrets.sh batch
    else
        log_warning "Secrets setup script not found"
    fi
    
    log_success "Google Cloud resources setup completed"
}

# Setup local development environment
setup_local_environment() {
    log_step "Setting up local development environment..."
    
    # Install dependencies
    log_info "Installing Node.js dependencies..."
    npm ci
    
    # Build CSS
    log_info "Building CSS..."
    npm run build:css-prod
    
    # Create local environment file
    if [ ! -f ".env" ]; then
        log_info "Creating local environment file..."
        cat > .env << EOF
# SafeHaven Local Development Environment
NODE_ENV=development
PORT=8080
LOG_LEVEL=debug

# Add your local configuration here
# Copy values from config/environments/development.env as needed
EOF
        log_success "Created .env file - please configure it with your local settings"
    else
        log_info ".env file already exists"
    fi
    
    log_success "Local environment setup completed"
}

# Test Docker build
test_docker_build() {
    log_step "Testing Docker build..."
    
    log_info "Building development Docker image..."
    docker build --target development -t safehaven-dev . || {
        log_error "Development Docker build failed"
        return 1
    }
    
    log_info "Building production Docker image..."
    docker build --target production \
        --build-arg EXPO_PUBLIC_FIREBASE_PROJECT_ID="test-project" \
        --build-arg EXPO_PUBLIC_FIREBASE_API_KEY="test-key" \
        --build-arg EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN="test.firebaseapp.com" \
        --build-arg EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM="5" \
        -t safehaven-prod . || {
        log_error "Production Docker build failed"
        return 1
    }
    
    log_success "Docker build test completed"
}

# Setup GitHub Actions secrets
setup_github_secrets() {
    log_step "Setting up GitHub Actions secrets..."
    
    echo
    echo "=== GitHub Repository Secrets Setup ==="
    echo "Please add the following secrets to your GitHub repository:"
    echo "Repository Settings > Secrets and variables > Actions > New repository secret"
    echo
    echo "Required secrets:"
    echo "1. GCP_PROJECT_ID: $PROJECT_ID"
    echo "2. GCP_SA_KEY: (contents of github-actions-key.json)"
    echo
    echo "Optional secrets:"
    echo "3. SLACK_WEBHOOK_URL: (for deployment notifications)"
    echo "4. CODECOV_TOKEN: (for code coverage reporting)"
    echo "5. SNYK_TOKEN: (for security scanning)"
    echo
    
    if [ -f "github-actions-key.json" ]; then
        echo "GitHub Actions service account key file found: github-actions-key.json"
        echo "Copy the contents of this file to the GCP_SA_KEY secret in GitHub."
        echo
        log_warning "Remember to delete the local key file after adding it to GitHub secrets!"
    else
        log_warning "GitHub Actions service account key file not found"
        echo "Run the Artifact Registry setup script to create it."
    fi
    
    read -p "Press Enter after you have added the secrets to GitHub..."
}

# Test deployment pipeline
test_deployment_pipeline() {
    log_step "Testing deployment pipeline..."
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_warning "Not in a git repository - skipping pipeline test"
        return 0
    fi
    
    # Check if GitHub Actions workflow exists
    if [ ! -f ".github/workflows/deploy.yml" ]; then
        log_warning "GitHub Actions workflow not found - skipping pipeline test"
        return 0
    fi
    
    log_info "GitHub Actions workflow found"
    log_info "To test the deployment pipeline:"
    echo "1. Commit and push your changes to the repository"
    echo "2. Create a pull request to test the security scan and tests"
    echo "3. Merge to main branch to trigger deployment"
    echo "4. Monitor the deployment in GitHub Actions"
    
    log_success "Deployment pipeline test instructions provided"
}

# Display setup summary
display_setup_summary() {
    log_success "SafeHaven deployment setup completed!"
    
    echo
    echo "=== Setup Summary ==="
    echo "✅ Google Cloud Project: $PROJECT_ID"
    echo "✅ Region: $REGION"
    echo "✅ Artifact Registry: ${REGION}-docker.pkg.dev/${PROJECT_ID}/safehaven-repo"
    echo "✅ Local development environment configured"
    echo "✅ Docker build tested"
    echo "✅ GitHub Actions workflow configured"
    echo
    echo "=== Next Steps ==="
    echo "1. Configure your Firebase project and update secrets"
    echo "2. Set up Google Maps API key"
    echo "3. Configure Twilio account for SMS alerts"
    echo "4. Add GitHub repository secrets"
    echo "5. Test the deployment pipeline"
    echo
    echo "=== Quick Commands ==="
    echo "• Start local development: npm run dev"
    echo "• Build for production: npm run build"
    echo "• Run tests: npm test"
    echo "• Deploy manually: ./deploy_cloud_run.sh"
    echo "• View logs: gcloud logs read --service=safehaven-dashboard"
    echo
    echo "=== Documentation ==="
    echo "• Deployment Guide: DEPLOYMENT_GUIDE.md"
    echo "• Build Guide: BUILD_GUIDE.md"
    echo "• Component Usage: COMPONENT_USAGE_GUIDE.md"
    echo
    echo "🚀 Your SafeHaven deployment pipeline is ready!"
}

# Main execution
main() {
    display_banner
    
    log_info "Starting SafeHaven deployment setup..."
    echo "Project ID: $PROJECT_ID"
    echo "Region: $REGION"
    echo "GitHub Repository: $GITHUB_REPO"
    echo
    
    read -p "Do you want to proceed with the setup? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Setup cancelled by user"
        exit 0
    fi
    
    check_prerequisites
    setup_gcp_resources
    setup_local_environment
    test_docker_build
    setup_github_secrets
    test_deployment_pipeline
    display_setup_summary
    
    log_success "Setup completed successfully!"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
