import React, { useState, useEffect } from 'react';

const Alerts = () => {
  const [alerts, setAlerts] = useState([]);
  const [filteredAlerts, setFilteredAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    // Simplified loading with timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      setLoading(false);
      setAlerts([
        { id: 1, title: 'Flood Warning', type: 'natural_disaster', severity: 'high', createdAt: Date.now() },
        { id: 2, title: 'Fire Alert', type: 'fire', severity: 'critical', createdAt: Date.now() - 3600000 },
        { id: 3, title: 'Medical Emergency', type: 'medical', severity: 'medium', createdAt: Date.now() - 7200000 }
      ]);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, []);

  useEffect(() => {
    // Simple filtering logic
    let filtered = alerts;
    if (filter !== 'all') {
      filtered = alerts.filter(alert => alert.severity === filter);
    }
    setFilteredAlerts(filtered);
  }, [alerts, filter]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner"></div>
        <span className="ml-2">Loading alerts...</span>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Emergency Alerts</h1>
        <p className="text-gray-600">Monitor and manage active emergency alerts</p>
      </div>

      <div className="mb-4">
        <select 
          value={filter} 
          onChange={(e) => setFilter(e.target.value)}
          className="form-select"
        >
          <option value="all">All Alerts</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>
      </div>

      <div className="grid gap-4">
        {filteredAlerts.map(alert => (
          <div key={alert.id} className="card">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-gray-900">{alert.title}</h3>
                <p className="text-sm text-gray-600">Type: {alert.type}</p>
                <p className="text-sm text-gray-600">Severity: {alert.severity}</p>
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                alert.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {alert.severity.toUpperCase()}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Alerts;
