const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

// __dirname is already available in CommonJS

const app = express();
const PORT = process.env.PORT || 8080;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.tailwindcss.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Required for React development
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: [
        "'self'",
        "https://*.firebaseio.com",
        "https://*.googleapis.com",
        "https://*.google.com",
        "https://api.twilio.com",
        "wss://*.firebaseio.com"
      ],
      frameSrc: ["'self'", "https://*.google.com"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
    }
  },
  crossOriginEmbedderPolicy: false // Required for some Firebase features
}));

// CORS configuration
const allowedOrigins = process.env.NODE_ENV === 'production'
  ? [
      'https://safehaven-dashboard-safehaven-463909.a.run.app',
      'https://dashboard.safehaven.org',
      'https://staging.safehaven.org'
    ]
  : [
      'http://localhost:3000',
      'http://localhost:8080',
      'http://localhost:8081',
      'http://localhost:19006'
    ];

app.use(cors({
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Compression and parsing middleware
app.use(compression());
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files from dist directory
app.use(express.static(path.join(__dirname, '../dist')));

// Health check endpoints (required for Cloud Run)
app.get('/health', (req, res) => {
  res.status(200).send('healthy\n');
});

app.get('/readiness', (req, res) => {
  // Add any readiness checks here (database connections, etc.)
  res.status(200).json({
    status: 'ready',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Detailed API health endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    pid: process.pid
  });
});

// Emergency alerts API endpoints
app.get('/api/alerts', (req, res) => {
  // This would typically fetch from your database
  res.json({ 
    message: 'Emergency alerts endpoint',
    data: []
  });
});

app.post('/api/alerts', (req, res) => {
  // This would typically save to your database
  res.json({ 
    message: 'Alert created successfully',
    data: req.body
  });
});

// SOS endpoints
app.post('/api/sos', (req, res) => {
  // Handle SOS message creation
  res.json({ 
    message: 'SOS message received',
    data: req.body
  });
});

// Shelter endpoints
app.get('/api/shelters', (req, res) => {
  res.json({ 
    message: 'Shelters endpoint',
    data: []
  });
});

// Reports endpoints
app.get('/api/reports', (req, res) => {
  res.json({ 
    message: 'Reports endpoint',
    data: []
  });
});

// Catch-all handler: send back React's index.html file for SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err.stack);

  // Log error details for monitoring
  const errorDetails = {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString(),
    userAgent: req.get('User-Agent'),
    ip: req.ip
  };

  console.error('Error details:', JSON.stringify(errorDetails));

  res.status(err.status || 500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 SafeHaven server running on port ${PORT}`);
  console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Access at: http://0.0.0.0:${PORT}`);
  console.log(`💾 Memory usage: ${JSON.stringify(process.memoryUsage())}`);
});

// Handle server errors
server.on('error', (err) => {
  console.error('Server error:', err);
  process.exit(1);
});

module.exports = app;
