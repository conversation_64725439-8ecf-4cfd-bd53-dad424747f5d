# SafeHaven Emergency Management Dashboard
# Production Environment Configuration

# Application Settings
NODE_ENV=production
PORT=8080
LOG_LEVEL=info

# Security Settings
HELMET_ENABLED=true
CORS_ENABLED=true
RATE_LIMITING_ENABLED=true
SESSION_SECURE=true

# Performance Settings
COMPRESSION_ENABLED=true
CACHE_ENABLED=true
CACHE_TTL=3600

# Monitoring Settings
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
ERROR_REPORTING_ENABLED=true

# Database Settings
DB_POOL_SIZE=10
DB_TIMEOUT=30000
DB_RETRY_ATTEMPTS=3

# External Services
TWILIO_ENABLED=true
GOOGLE_MAPS_ENABLED=true
FIREBASE_ENABLED=true

# Alert Settings
DEFAULT_ALERT_RADIUS_KM=5
MAX_ALERT_RADIUS_KM=50
ALERT_BATCH_SIZE=100
ALERT_RETRY_ATTEMPTS=3

# File Upload Settings
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Settings
SESSION_TIMEOUT=3600000
SESSION_CLEANUP_INTERVAL=300000

# Backup Settings
BACKUP_ENABLED=true
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=30

# Feature Flags
FEATURE_REAL_TIME_ALERTS=true
FEATURE_GEOFENCING=true
FEATURE_MULTI_LANGUAGE=true
FEATURE_DARK_MODE=true
FEATURE_OFFLINE_MODE=true

# Cloud Run Specific
CLOUD_RUN_SERVICE_NAME=safehaven-dashboard
CLOUD_RUN_REGION=us-central1
CLOUD_RUN_MIN_INSTANCES=1
CLOUD_RUN_MAX_INSTANCES=100
CLOUD_RUN_CONCURRENCY=80
CLOUD_RUN_TIMEOUT=300

# Scaling Settings
AUTO_SCALING_ENABLED=true
SCALE_UP_THRESHOLD=80
SCALE_DOWN_THRESHOLD=20
SCALE_UP_COOLDOWN=300
SCALE_DOWN_COOLDOWN=600
