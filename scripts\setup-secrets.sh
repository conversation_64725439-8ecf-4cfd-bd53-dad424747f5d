#!/bin/bash

# SafeHaven Emergency Management Dashboard
# Google Cloud Secret Manager Setup Script
# This script creates and manages secrets required for the application

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-safehaven-463909}"
REGION="${REGION:-us-central1}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "No active gcloud authentication found. Please run 'gcloud auth login'"
        exit 1
    fi
    
    # Set the project
    gcloud config set project "$PROJECT_ID"
    log_success "Prerequisites check completed"
}

# Enable Secret Manager API
enable_secret_manager() {
    log_info "Enabling Secret Manager API..."
    gcloud services enable secretmanager.googleapis.com --project="$PROJECT_ID"
    log_success "Secret Manager API enabled"
}

# Create a secret if it doesn't exist
create_secret() {
    local secret_name="$1"
    local secret_value="$2"
    local description="$3"
    
    # Check if secret already exists
    if gcloud secrets describe "$secret_name" --project="$PROJECT_ID" &>/dev/null; then
        log_warning "Secret $secret_name already exists. Updating with new value..."
        echo "$secret_value" | gcloud secrets versions add "$secret_name" \
            --data-file=- \
            --project="$PROJECT_ID"
    else
        log_info "Creating secret: $secret_name"
        echo "$secret_value" | gcloud secrets create "$secret_name" \
            --data-file=- \
            --replication-policy="automatic" \
            --project="$PROJECT_ID"
        
        # Add description as label
        gcloud secrets update "$secret_name" \
            --update-labels="description=$description" \
            --project="$PROJECT_ID" || true
    fi
    
    log_success "Secret $secret_name created/updated"
}

# Prompt for secret value
prompt_for_secret() {
    local secret_name="$1"
    local description="$2"
    local is_sensitive="${3:-true}"
    
    echo
    log_info "Setting up: $secret_name"
    echo "Description: $description"
    echo -n "Enter value: "
    
    if [ "$is_sensitive" = "true" ]; then
        read -s secret_value
        echo
    else
        read secret_value
    fi
    
    if [ -z "$secret_value" ]; then
        log_warning "Empty value provided for $secret_name. Skipping..."
        return 1
    fi
    
    create_secret "$secret_name" "$secret_value" "$description"
}

# Setup Firebase secrets
setup_firebase_secrets() {
    log_info "Setting up Firebase configuration secrets..."
    
    echo
    echo "=== Firebase Configuration ==="
    echo "Please provide your Firebase project configuration values."
    echo "You can find these in your Firebase Console > Project Settings > General > Your apps"
    echo
    
    prompt_for_secret "firebase-api-key" "Firebase Web API Key" true
    prompt_for_secret "firebase-auth-domain" "Firebase Auth Domain (e.g., your-project.firebaseapp.com)" false
    prompt_for_secret "firebase-project-id" "Firebase Project ID" false
    prompt_for_secret "firebase-storage-bucket" "Firebase Storage Bucket (e.g., your-project.appspot.com)" false
    prompt_for_secret "firebase-messaging-sender-id" "Firebase Messaging Sender ID" false
    prompt_for_secret "firebase-app-id" "Firebase App ID" false
    prompt_for_secret "firebase-measurement-id" "Firebase Measurement ID (Google Analytics)" false
    prompt_for_secret "firebase-database-url" "Firebase Realtime Database URL" false
}

# Setup Google Maps secrets
setup_google_maps_secrets() {
    log_info "Setting up Google Maps configuration secrets..."
    
    echo
    echo "=== Google Maps Configuration ==="
    echo "Please provide your Google Maps API key."
    echo "You can create one in Google Cloud Console > APIs & Services > Credentials"
    echo
    
    prompt_for_secret "google-maps-api-key" "Google Maps API Key" true
}

# Setup Twilio secrets
setup_twilio_secrets() {
    log_info "Setting up Twilio configuration secrets..."
    
    echo
    echo "=== Twilio Configuration ==="
    echo "Please provide your Twilio account credentials."
    echo "You can find these in your Twilio Console > Account > API keys & tokens"
    echo
    
    prompt_for_secret "twilio-account-sid" "Twilio Account SID" false
    prompt_for_secret "twilio-auth-token" "Twilio Auth Token" true
    prompt_for_secret "twilio-phone-number" "Twilio Phone Number (e.g., +**********)" false
}

# Setup application secrets
setup_app_secrets() {
    log_info "Setting up application configuration secrets..."
    
    echo
    echo "=== Application Configuration ==="
    
    # Default alert radius
    create_secret "default-alert-radius-km" "5" "Default alert radius in kilometers"
    
    # JWT secret for session management
    local jwt_secret=$(openssl rand -base64 32)
    create_secret "jwt-secret" "$jwt_secret" "JWT secret for session management"
    
    # Database encryption key
    local db_encryption_key=$(openssl rand -base64 32)
    create_secret "db-encryption-key" "$db_encryption_key" "Database encryption key"
}

# Grant access to service accounts
grant_secret_access() {
    log_info "Granting secret access to service accounts..."
    
    local service_accounts=(
        "safehaven-dashboard@${PROJECT_ID}.iam.gserviceaccount.com"
        "github-actions-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    )
    
    local secrets=(
        "firebase-api-key"
        "firebase-auth-domain"
        "firebase-project-id"
        "firebase-storage-bucket"
        "firebase-messaging-sender-id"
        "firebase-app-id"
        "firebase-measurement-id"
        "firebase-database-url"
        "google-maps-api-key"
        "twilio-account-sid"
        "twilio-auth-token"
        "twilio-phone-number"
        "default-alert-radius-km"
        "jwt-secret"
        "db-encryption-key"
    )
    
    for sa in "${service_accounts[@]}"; do
        for secret in "${secrets[@]}"; do
            # Check if secret exists before granting access
            if gcloud secrets describe "$secret" --project="$PROJECT_ID" &>/dev/null; then
                gcloud secrets add-iam-policy-binding "$secret" \
                    --member="serviceAccount:$sa" \
                    --role="roles/secretmanager.secretAccessor" \
                    --project="$PROJECT_ID" \
                    --quiet || log_warning "Failed to grant access to $secret for $sa"
            fi
        done
    done
    
    log_success "Secret access granted to service accounts"
}

# List all secrets
list_secrets() {
    log_info "Listing all created secrets..."
    
    echo
    echo "=== Created Secrets ==="
    gcloud secrets list --project="$PROJECT_ID" --format="table(name,createTime,labels.description)"
    echo
}

# Display usage instructions
display_usage() {
    echo
    echo "=== Usage Instructions ==="
    echo
    echo "To access secrets in your application:"
    echo "1. Use the Google Cloud Secret Manager client library"
    echo "2. Or use gcloud command: gcloud secrets versions access latest --secret=SECRET_NAME"
    echo
    echo "To update a secret:"
    echo "echo 'new-value' | gcloud secrets versions add SECRET_NAME --data-file=-"
    echo
    echo "To view secret metadata:"
    echo "gcloud secrets describe SECRET_NAME"
    echo
}

# Interactive setup mode
interactive_setup() {
    echo
    echo "=== SafeHaven Secret Manager Setup ==="
    echo "This script will help you set up all required secrets for the SafeHaven application."
    echo
    
    read -p "Do you want to proceed with interactive setup? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Setup cancelled by user"
        exit 0
    fi
    
    setup_firebase_secrets
    setup_google_maps_secrets
    setup_twilio_secrets
    setup_app_secrets
    grant_secret_access
    list_secrets
    display_usage
    
    log_success "Secret setup completed successfully!"
}

# Batch setup mode (for CI/CD)
batch_setup() {
    log_info "Running batch setup mode..."
    
    # Only create application secrets that don't require user input
    setup_app_secrets
    grant_secret_access
    
    log_success "Batch setup completed!"
    log_warning "Please run interactive setup to configure Firebase, Google Maps, and Twilio secrets"
}

# Main execution
main() {
    check_prerequisites
    enable_secret_manager
    
    # Check if running in interactive mode
    if [ "${1:-interactive}" = "batch" ]; then
        batch_setup
    else
        interactive_setup
    fi
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
