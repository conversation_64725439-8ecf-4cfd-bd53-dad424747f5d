import React, { useState, useEffect } from 'react';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { app } from '../src/config/firebase';
import { signInWithEmail, getUserProfile, signOut, handleGoogleRedirectResult } from '../src/services/auth';
import { UserRole } from '../src/config/firebase';

// Import Compiled CSS (with proper styling and scrolling)
import './styles-compiled.css';

// Components
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Dashboard from './pages/Dashboard';
import Alerts from './pages/Alerts';
import SOSMessages from './pages/SOSMessages';
import Reports from './pages/Reports';
import Shelters from './pages/Shelters';
import CreateAlert from './pages/CreateAlert';
import Maps from './pages/Maps';

// Initialize Firebase Auth
const auth = getAuth(app);

const App = () => {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [authMode, setAuthMode] = useState('login'); // 'login' or 'signup'
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    // Handle Google Sign-In redirect result on app load
    const handleRedirect = async () => {
      try {
        const googleUser = await handleGoogleRedirectResult();
        if (googleUser) {
          setUser(googleUser);
          const profile = await getUserProfile(googleUser.uid);
          setUserProfile(profile);
          setLoading(false);
          return;
        }
      } catch (error) {
        console.error('Error handling Google redirect:', error);
      }
    };

    handleRedirect();

    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      setUser(currentUser);

      if (currentUser) {
        // Get user profile to determine role
        const profile = await getUserProfile(currentUser.uid);
        setUserProfile(profile);
      } else {
        setUserProfile(null);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const handleLogin = async (email, password) => {
    try {
      await signInWithEmail(email, password);
      // Don't set currentPage here - let the auth state change handle it
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const handleSignup = () => {
    // Don't set currentPage here - let the auth state change handle it
  };

  const handleSwitchToSignup = () => {
    setAuthMode('signup');
  };

  const handleSwitchToLogin = () => {
    setAuthMode('login');
  };

  const handleLogout = async () => {
    try {
      await signOut();
      setCurrentPage('login');
      setAuthMode('login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Check if user has admin privileges
  const isAdmin = userProfile?.role === UserRole.ADMIN;

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'alerts':
        return <Alerts />;
      case 'sos-messages':
        return <SOSMessages />;
      case 'reports':
        return <Reports />;
      case 'shelters':
        return <Shelters />;
      case 'maps':
        return <Maps />;
      case 'create-alert':
        return <CreateAlert onNavigate={setCurrentPage} />;
      default:
        return <Dashboard />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
            <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-400 animate-ping"></div>
          </div>
          <p className="mt-6 text-lg font-medium text-gray-700">Loading SafeHaven...</p>
          <p className="mt-2 text-sm text-gray-500">Emergency Management System</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {user ? (
        <div className="app-container">
          {/* Mobile Sidebar Overlay */}
          {sidebarOpen && (
            <div
              className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden transition-opacity duration-300"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Sidebar */}
          <div className={`sidebar ${sidebarOpen ? 'open' : ''}`}>
            <Sidebar
              currentPage={currentPage}
              onNavigate={(page) => {
                setCurrentPage(page);
                setSidebarOpen(false); // Close sidebar on mobile after navigation
              }}
              isAdmin={isAdmin}
              userProfile={userProfile}
            />
          </div>

          {/* Main Content Area */}
          <div className="main-content">
            <div className="header">
              <Header
                user={user}
                userProfile={userProfile}
                onLogout={handleLogout}
                onMenuClick={() => setSidebarOpen(!sidebarOpen)}
              />
            </div>
            
            {/* Scrollable Main Content */}
            <main className="content-area scrollable">
              <div className="min-h-full">
                {renderCurrentPage()}
              </div>
            </main>
          </div>
        </div>
      ) : (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
          {authMode === 'login' ? (
            <Login
              onLogin={handleLogin}
              onSwitchToSignup={handleSwitchToSignup}
            />
          ) : (
            <Signup
              onSignup={handleSignup}
              onSwitchToLogin={handleSwitchToLogin}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default App;
