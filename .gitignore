# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*
*-debug.log
firebase-debug.log
pglite-debug.log

# macOS
.DS_Store
*.pem

# local env files
.env
.env*.local

# vscode
.vscode/

# typescript
*.tsbuildinfo

.config/
