<div className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 ${getColorClasses(color)}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-4">
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getIconBgClass(color)}`}>
              <IconComponent className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">{title}</h3>
              <p className="text-xs text-gray-500 mt-1">{description}</p>
            </div>
          </div>

          <div className="flex items-end justify-between">
            <div>
              <div className="text-3xl font-bold text-gray-900 mb-1">{value}</div>
              {trend && (
                <div className={`flex items-center space-x-1 text-sm ${trend.positive ? 'text-green-600' : 'text-red-600'}`}>
                  <TrendingUp className={`w-4 h-4 ${trend.positive ? '' : 'rotate-180'}`} />
                  <span className="font-medium">{trend.value}</span>
                  <span className="text-gray-500">vs last hour</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
=======
  const StatCard = ({ title, value, icon: IconComponent, color, description, trend }) => (
    <div className={`bg-white rounded-2xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-300 border-l-8 ${getColorClasses(color)}`}>
      <div className="flex items-center space-x-4">
        <div className={`w-14 h-14 rounded-lg flex items-center justify-center ${getIconBgClass(color)}`}>
          <IconComponent className="w-7 h-7" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 uppercase tracking-wide">{title}</h3>
          <p className="text-sm text-gray-500 mt-1">{description}</p>
          <div className="mt-2 flex items-center space-x-2">
            <div className="text-4xl font-extrabold text-gray-900">{value}</div>
            {trend && (
              <div className={`flex items-center space-x-1 text-sm font-medium ${trend.positive ? 'text-green-600' : 'text-red-600'}`}>
                <TrendingUp className={`w-5 h-5 ${trend.positive ? '' : 'rotate-180'}`} />
                <span>{trend.value}</span>
                <span className="text-gray-400">vs last hour</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
