# SafeHaven Component Usage Guide

## Overview
This guide provides detailed instructions for using the redesigned SafeHaven UI components, following the established design system and best practices for emergency management interfaces.

## Component Library

### 1. Header Component

**Location**: `web/components/Header.js`

**Features**:
- Responsive navigation with mobile hamburger menu
- Real-time clock display
- System status indicators
- User profile with role-based badges
- Notification center
- Proper accessibility support

**Usage**:
```jsx
<Header
  user={user}
  userProfile={userProfile}
  onLogout={handleLogout}
  onMenuClick={() => setSidebarOpen(!sidebarOpen)}
/>
```

**Props**:
- `user`: Firebase user object
- `userProfile`: User profile with role information
- `onLogout`: Function to handle user logout
- `onMenuClick`: Function to toggle mobile sidebar

### 2. Sidebar Component

**Location**: `web/components/Sidebar.js`

**Features**:
- Role-based navigation filtering
- Modern icon system using Lucide React
- Active state indicators
- Notification badges for urgent items
- User role display with visual indicators
- System status footer

**Usage**:
```jsx
<Sidebar
  currentPage={currentPage}
  onNavigate={(page) => setCurrentPage(page)}
  isAdmin={isAdmin}
  userProfile={userProfile}
/>
```

**Props**:
- `currentPage`: Current active page identifier
- `onNavigate`: Function to handle navigation
- `isAdmin`: Boolean indicating admin privileges
- `userProfile`: User profile object

### 3. Dashboard Component

**Location**: `web/pages/Dashboard.js`

**Features**:
- Modern statistics cards with trend indicators
- Real-time activity feed
- Quick action buttons
- System statistics panel
- Responsive grid layout
- Loading states and empty states

**Key Elements**:
- **StatCard**: Displays key metrics with icons and trends
- **Recent Activity**: Live feed of system events
- **Quick Actions**: Primary action buttons
- **System Statistics**: Performance metrics

## Design System Implementation

### Color Usage

**Status Colors**:
```css
/* Success/Normal Operations */
.text-green-600, .bg-green-50, .border-green-200

/* Warning/Medium Priority */
.text-orange-600, .bg-orange-50, .border-orange-200

/* Danger/Critical */
.text-red-600, .bg-red-50, .border-red-200

/* Information */
.text-blue-600, .bg-blue-50, .border-blue-200
```

**Role-Based Colors**:
```css
/* Admin/Emergency Coordinator */
.text-red-700, .bg-red-50, .border-red-200

/* User/Emergency Observer */
.text-blue-700, .bg-blue-50, .border-blue-200
```

### Typography Scale

```css
/* Page Titles */
.text-3xl .font-bold

/* Section Headers */
.text-xl .font-semibold

/* Card Titles */
.text-lg .font-semibold

/* Body Text */
.text-sm .font-medium

/* Secondary Text */
.text-xs .text-gray-500
```

### Spacing System

```css
/* Component Padding */
.p-6 /* Large components */
.p-4 /* Medium components */
.p-3 /* Small components */

/* Element Spacing */
.space-x-3, .space-y-3 /* Default spacing */
.space-x-6, .space-y-6 /* Large spacing */
```

## Responsive Breakpoints

### Mobile (< 768px)
- Single column layout
- Collapsible sidebar overlay
- Touch-friendly button sizes (min 44px)
- Simplified navigation
- Stacked statistics cards

### Tablet (768px - 1024px)
- Two-column layout for statistics
- Collapsible sidebar
- Optimized touch targets
- Responsive typography

### Desktop (> 1024px)
- Full multi-column layout
- Persistent sidebar
- Hover states and animations
- Maximum content density

## Icon System

**Using Lucide React Icons**:
```jsx
import { AlertTriangle, User, Settings } from 'lucide-react';

// Standard size for navigation
<AlertTriangle className="w-5 h-5" />

// Large size for emphasis
<AlertTriangle className="w-6 h-6" />

// Small size for inline elements
<AlertTriangle className="w-4 h-4" />
```

**Icon Categories**:
- **Navigation**: `BarChart3`, `AlertTriangle`, `FileText`, `Home`, `Map`
- **Actions**: `Plus`, `Eye`, `Settings`, `LogOut`
- **Status**: `CheckCircle`, `AlertCircle`, `Clock`, `Activity`
- **User**: `User`, `Shield`, `Bell`

## Accessibility Features

### Focus Management
- Visible focus rings on all interactive elements
- Logical tab order
- Skip links for keyboard navigation

### Screen Reader Support
- Proper ARIA labels
- Semantic HTML structure
- Status announcements for dynamic content

### Color Contrast
- WCAG 2.1 AA compliant color combinations
- Text contrast ratio > 4.5:1
- Interactive element contrast > 3:1

## Animation Guidelines

### Transitions
```css
/* Standard transition */
.transition-all .duration-200 .ease-in-out

/* Hover effects */
.hover:shadow-md .hover:-translate-y-1

/* Loading states */
.animate-pulse .animate-spin
```

### Performance
- Use `transform` and `opacity` for animations
- Avoid animating layout properties
- Respect `prefers-reduced-motion`

## Best Practices

### Component Structure
1. Import all required dependencies
2. Define prop types and defaults
3. Implement loading and error states
4. Add proper accessibility attributes
5. Include responsive design considerations

### State Management
1. Use local state for UI-only data
2. Lift state up when shared between components
3. Implement proper error boundaries
4. Handle loading states gracefully

### Performance
1. Lazy load non-critical components
2. Optimize images and icons
3. Use React.memo for expensive components
4. Implement proper key props for lists

## Testing Guidelines

### Visual Testing
1. Test on all supported breakpoints
2. Verify color contrast ratios
3. Check focus states and keyboard navigation
4. Test with screen readers

### Functional Testing
1. Test all interactive elements
2. Verify responsive behavior
3. Test error states and edge cases
4. Validate accessibility features

## Maintenance

### Regular Updates
1. Keep Lucide React icons updated
2. Monitor Tailwind CSS updates
3. Review accessibility guidelines
4. Update browser support matrix

### Code Quality
1. Follow consistent naming conventions
2. Document complex logic
3. Remove unused CSS classes
4. Optimize bundle size

## Future Enhancements

### Planned Features
1. Dark mode support
2. Advanced data visualizations
3. Real-time collaboration features
4. Enhanced mobile experience

### Technical Debt
1. Migrate remaining emoji icons to Lucide
2. Implement comprehensive testing suite
3. Add Storybook for component documentation
4. Optimize performance metrics
