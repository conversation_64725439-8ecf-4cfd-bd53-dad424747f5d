# SafeHaven Design System

## Overview
Modern, accessible design system for SafeHaven emergency management dashboard following Material Design 3 principles with government/NGO-appropriate styling.

## Color Palette

### Primary Colors
- **Primary Blue**: `#1e40af` (blue-800) - Main brand color
- **Primary Light**: `#3b82f6` (blue-600) - Interactive elements
- **Primary Dark**: `#1e3a8a` (blue-900) - Headers, emphasis

### Status Colors
- **Success**: `#059669` (emerald-600) - Normal operations, active shelters
- **Warning**: `#d97706` (amber-600) - Medium alerts, capacity warnings
- **Danger**: `#dc2626` (red-600) - Critical alerts, emergencies
- **Info**: `#0284c7` (sky-600) - Information, reports

### Neutral Colors
- **Gray 50**: `#f8fafc` - Background
- **Gray 100**: `#f1f5f9` - Card backgrounds
- **Gray 200**: `#e2e8f0` - Borders
- **Gray 600**: `#475569` - Secondary text
- **Gray 900**: `#0f172a` - Primary text

## Typography

### Font Family
- **Primary**: Inter (Google Fonts)
- **Fallback**: system-ui, -apple-system, sans-serif

### Scale
- **Display**: `text-4xl` (36px) - Page titles
- **Heading 1**: `text-3xl` (30px) - Section headers
- **Heading 2**: `text-2xl` (24px) - Card titles
- **Heading 3**: `text-xl` (20px) - Subsections
- **Body Large**: `text-lg` (18px) - Important content
- **Body**: `text-base` (16px) - Default text
- **Body Small**: `text-sm` (14px) - Secondary text
- **Caption**: `text-xs` (12px) - Labels, metadata

### Weights
- **Light**: `font-light` (300)
- **Regular**: `font-normal` (400)
- **Medium**: `font-medium` (500)
- **Semibold**: `font-semibold` (600)
- **Bold**: `font-bold` (700)

## Spacing System

### Base Unit: 4px (0.25rem)
- **xs**: `1` (4px)
- **sm**: `2` (8px)
- **md**: `4` (16px)
- **lg**: `6` (24px)
- **xl**: `8` (32px)
- **2xl**: `12` (48px)
- **3xl**: `16` (64px)

## Component Specifications

### Cards
- **Background**: `bg-white`
- **Border**: `border border-gray-200`
- **Radius**: `rounded-xl` (12px)
- **Shadow**: `shadow-sm hover:shadow-md`
- **Padding**: `p-6`

### Buttons
#### Primary
- **Background**: `bg-blue-600 hover:bg-blue-700`
- **Text**: `text-white font-medium`
- **Padding**: `px-4 py-2` (small), `px-6 py-3` (medium)
- **Radius**: `rounded-lg`

#### Secondary
- **Background**: `bg-white hover:bg-gray-50`
- **Border**: `border border-gray-300`
- **Text**: `text-gray-700 font-medium`

#### Danger
- **Background**: `bg-red-600 hover:bg-red-700`
- **Text**: `text-white font-medium`

### Status Badges
- **Padding**: `px-3 py-1`
- **Radius**: `rounded-full`
- **Font**: `text-xs font-semibold uppercase tracking-wide`

### Navigation
- **Active State**: `bg-blue-50 text-blue-700 border-l-4 border-blue-600`
- **Hover State**: `hover:bg-gray-50 hover:text-gray-900`
- **Icon Size**: `w-5 h-5` (20px)

## Responsive Breakpoints

### Mobile First Approach
- **Mobile**: `< 768px` - Single column, stacked navigation
- **Tablet**: `768px - 1024px` - Two columns, collapsible sidebar
- **Desktop**: `> 1024px` - Full layout, persistent sidebar

### Grid System
- **Mobile**: `grid-cols-1`
- **Tablet**: `md:grid-cols-2`
- **Desktop**: `lg:grid-cols-3 xl:grid-cols-4`

## Accessibility Standards

### WCAG 2.1 AA Compliance
- **Contrast Ratio**: Minimum 4.5:1 for normal text
- **Touch Targets**: Minimum 44px for interactive elements
- **Focus States**: Visible focus indicators on all interactive elements
- **Screen Reader**: Proper ARIA labels and semantic HTML

### Focus States
- **Ring**: `focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`
- **Outline**: `focus:outline-none` (when ring is present)

## Animation & Transitions

### Duration
- **Fast**: `duration-150` (150ms) - Hover states
- **Normal**: `duration-200` (200ms) - Default transitions
- **Slow**: `duration-300` (300ms) - Layout changes

### Easing
- **Default**: `ease-in-out`
- **Enter**: `ease-out`
- **Exit**: `ease-in`

### Common Transitions
- **Hover**: `transition-all duration-200 ease-in-out`
- **Transform**: `transition-transform duration-200`
- **Colors**: `transition-colors duration-150`

## Icon System

### Lucide React Icons
- **Size**: `w-5 h-5` (20px) for navigation, `w-6 h-6` (24px) for actions
- **Stroke Width**: `stroke-2` (2px)
- **Color**: Inherit from parent text color

### Emergency Icons
- **Alert**: `AlertTriangle`
- **SOS**: `AlertCircle`
- **Shelter**: `Home`
- **Reports**: `FileText`
- **Maps**: `Map`
- **Dashboard**: `BarChart3`
- **User**: `User`
- **Settings**: `Settings`

## Layout Patterns

### Dashboard Grid
```css
.dashboard-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}
```

### Card Layout
```css
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200;
}
```

### Sidebar Layout
```css
.sidebar {
  @apply w-64 bg-white shadow-lg border-r border-gray-200 flex flex-col h-full;
}
```

## Component States

### Loading
- **Skeleton**: `animate-pulse bg-gray-200`
- **Spinner**: `animate-spin`

### Empty States
- **Background**: `bg-gray-50`
- **Text**: `text-gray-500`
- **Icon**: `text-gray-400`

### Error States
- **Background**: `bg-red-50`
- **Border**: `border-red-200`
- **Text**: `text-red-700`

## Usage Guidelines

### Do's
- Use consistent spacing from the spacing system
- Follow the color palette for all UI elements
- Implement proper focus states for accessibility
- Use semantic HTML elements
- Test on all breakpoints

### Don'ts
- Don't use arbitrary values for spacing or colors
- Don't mix emoji with proper icons
- Don't ignore accessibility requirements
- Don't create custom CSS when Tailwind classes exist
- Don't forget mobile-first responsive design

## Implementation Notes

### Tailwind Configuration
- Custom colors are defined in `tailwind.config.js`
- Inter font is loaded via Google Fonts
- Custom animations are defined for specific use cases

### CSS Architecture
- Use `@layer components` for reusable component classes
- Prefer Tailwind utilities over custom CSS
- Keep custom CSS minimal and well-documented
