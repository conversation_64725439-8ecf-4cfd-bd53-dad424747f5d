console.log('index.js is loading...');

import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';

// Import styles
import './styles.css';

console.log('All imports successful');

// Get the root element
const rootElement = document.getElementById('root');
console.log('Root element:', rootElement);

if (rootElement) {
  try {
    console.log('Creating React root...');
    const root = createRoot(rootElement);
    console.log('Rendering App component...');
    root.render(React.createElement(App));
    console.log('App rendered successfully');
  } catch (error) {
    console.error('Error rendering React app:', error);
    
    // Fallback: Show error message on page
    rootElement.innerHTML = `
      <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
        <h1 style="color: #dc2626; margin-bottom: 20px;">SafeHaven Loading Error</h1>
        <p style="color: #6b7280; margin-bottom: 10px;">There was an error loading the application:</p>
        <pre style="background: #f3f4f6; padding: 10px; border-radius: 5px; text-align: left; overflow-x: auto;">${error.message}</pre>
        <p style="color: #6b7280; margin-top: 20px;">Please check the browser console for more details.</p>
      </div>
    `;
  }
} else {
  console.error('Root element not found');
} 