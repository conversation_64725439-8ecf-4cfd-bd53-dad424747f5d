/**
 * SafeHaven Emergency Management Dashboard
 * Configuration Loader
 * 
 * This module loads environment-specific configuration and manages secrets
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

class ConfigLoader {
  constructor() {
    this.environment = process.env.NODE_ENV || 'development';
    this.config = {};
    this.secretsCache = new Map();
    this.loadConfiguration();
  }

  /**
   * Load configuration from environment files and secrets
   */
  loadConfiguration() {
    try {
      // Load base configuration
      this.loadEnvironmentFile();
      
      // Load secrets if in production/staging
      if (this.environment !== 'development') {
        this.loadSecrets();
      }
      
      // Validate required configuration
      this.validateConfiguration();
      
      console.log(`✅ Configuration loaded for environment: ${this.environment}`);
    } catch (error) {
      console.error('❌ Failed to load configuration:', error.message);
      process.exit(1);
    }
  }

  /**
   * Load environment-specific configuration file
   */
  loadEnvironmentFile() {
    const envFile = path.join(__dirname, 'environments', `${this.environment}.env`);
    
    if (fs.existsSync(envFile)) {
      const result = dotenv.config({ path: envFile });
      if (result.error) {
        throw new Error(`Failed to load environment file: ${result.error.message}`);
      }
      console.log(`📁 Loaded environment file: ${envFile}`);
    } else {
      console.warn(`⚠️  Environment file not found: ${envFile}`);
    }

    // Also load .env file if it exists (for local overrides)
    const localEnvFile = path.join(process.cwd(), '.env');
    if (fs.existsSync(localEnvFile)) {
      dotenv.config({ path: localEnvFile });
      console.log(`📁 Loaded local environment file: ${localEnvFile}`);
    }
  }

  /**
   * Load secrets from Google Cloud Secret Manager
   */
  async loadSecrets() {
    if (this.environment === 'development') {
      return;
    }

    try {
      const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');
      const client = new SecretManagerServiceClient();
      
      const projectId = process.env.GOOGLE_CLOUD_PROJECT || process.env.PROJECT_ID;
      if (!projectId) {
        console.warn('⚠️  No Google Cloud project ID found, skipping secret loading');
        return;
      }

      const secrets = [
        'firebase-api-key',
        'firebase-auth-domain',
        'firebase-project-id',
        'firebase-storage-bucket',
        'firebase-messaging-sender-id',
        'firebase-app-id',
        'firebase-measurement-id',
        'firebase-database-url',
        'google-maps-api-key',
        'twilio-account-sid',
        'twilio-auth-token',
        'twilio-phone-number',
        'jwt-secret',
        'db-encryption-key'
      ];

      for (const secretName of secrets) {
        try {
          const [version] = await client.accessSecretVersion({
            name: `projects/${projectId}/secrets/${secretName}/versions/latest`,
          });

          const secretValue = version.payload.data.toString();
          this.secretsCache.set(secretName, secretValue);
          
          // Set as environment variable with EXPO_PUBLIC_ prefix for client-side secrets
          if (secretName.startsWith('firebase-') || secretName.startsWith('google-maps-') || secretName.startsWith('twilio-')) {
            const envVarName = `EXPO_PUBLIC_${secretName.replace(/-/g, '_').toUpperCase()}`;
            process.env[envVarName] = secretValue;
          } else {
            process.env[secretName.replace(/-/g, '_').toUpperCase()] = secretValue;
          }
          
        } catch (error) {
          console.warn(`⚠️  Failed to load secret ${secretName}: ${error.message}`);
        }
      }

      console.log(`🔐 Loaded ${this.secretsCache.size} secrets from Secret Manager`);
    } catch (error) {
      console.error('❌ Failed to initialize Secret Manager client:', error.message);
    }
  }

  /**
   * Validate required configuration
   */
  validateConfiguration() {
    const required = [
      'NODE_ENV',
      'PORT'
    ];

    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required configuration: ${missing.join(', ')}`);
    }

    // Validate Firebase configuration in production
    if (this.environment === 'production') {
      const firebaseRequired = [
        'EXPO_PUBLIC_FIREBASE_API_KEY',
        'EXPO_PUBLIC_FIREBASE_PROJECT_ID',
        'EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN'
      ];

      const firebaseMissing = firebaseRequired.filter(key => !process.env[key]);
      if (firebaseMissing.length > 0) {
        console.warn(`⚠️  Missing Firebase configuration: ${firebaseMissing.join(', ')}`);
      }
    }
  }

  /**
   * Get configuration value
   */
  get(key, defaultValue = null) {
    return process.env[key] || defaultValue;
  }

  /**
   * Get boolean configuration value
   */
  getBoolean(key, defaultValue = false) {
    const value = this.get(key);
    if (value === null) return defaultValue;
    return value.toLowerCase() === 'true';
  }

  /**
   * Get number configuration value
   */
  getNumber(key, defaultValue = 0) {
    const value = this.get(key);
    if (value === null) return defaultValue;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  }

  /**
   * Get array configuration value
   */
  getArray(key, defaultValue = [], separator = ',') {
    const value = this.get(key);
    if (value === null) return defaultValue;
    return value.split(separator).map(item => item.trim()).filter(Boolean);
  }

  /**
   * Get secret value
   */
  getSecret(secretName) {
    return this.secretsCache.get(secretName) || null;
  }

  /**
   * Check if running in production
   */
  isProduction() {
    return this.environment === 'production';
  }

  /**
   * Check if running in staging
   */
  isStaging() {
    return this.environment === 'staging';
  }

  /**
   * Check if running in development
   */
  isDevelopment() {
    return this.environment === 'development';
  }

  /**
   * Get all configuration as object
   */
  getAll() {
    return {
      environment: this.environment,
      app: {
        name: 'SafeHaven Emergency Dashboard',
        version: process.env.npm_package_version || '1.0.0',
        port: this.getNumber('PORT', 8080),
        logLevel: this.get('LOG_LEVEL', 'info')
      },
      security: {
        helmetEnabled: this.getBoolean('HELMET_ENABLED', true),
        corsEnabled: this.getBoolean('CORS_ENABLED', true),
        rateLimitingEnabled: this.getBoolean('RATE_LIMITING_ENABLED', true),
        sessionSecure: this.getBoolean('SESSION_SECURE', this.isProduction())
      },
      performance: {
        compressionEnabled: this.getBoolean('COMPRESSION_ENABLED', true),
        cacheEnabled: this.getBoolean('CACHE_ENABLED', this.isProduction()),
        cacheTtl: this.getNumber('CACHE_TTL', 3600)
      },
      monitoring: {
        metricsEnabled: this.getBoolean('METRICS_ENABLED', this.isProduction()),
        healthCheckEnabled: this.getBoolean('HEALTH_CHECK_ENABLED', true),
        errorReportingEnabled: this.getBoolean('ERROR_REPORTING_ENABLED', this.isProduction())
      },
      features: {
        realTimeAlerts: this.getBoolean('FEATURE_REAL_TIME_ALERTS', true),
        geofencing: this.getBoolean('FEATURE_GEOFENCING', true),
        multiLanguage: this.getBoolean('FEATURE_MULTI_LANGUAGE', false),
        darkMode: this.getBoolean('FEATURE_DARK_MODE', true),
        offlineMode: this.getBoolean('FEATURE_OFFLINE_MODE', false)
      },
      alerts: {
        defaultRadiusKm: this.getNumber('DEFAULT_ALERT_RADIUS_KM', 5),
        maxRadiusKm: this.getNumber('MAX_ALERT_RADIUS_KM', 50),
        batchSize: this.getNumber('ALERT_BATCH_SIZE', 100),
        retryAttempts: this.getNumber('ALERT_RETRY_ATTEMPTS', 3)
      }
    };
  }
}

// Create singleton instance
const configLoader = new ConfigLoader();

module.exports = configLoader;
