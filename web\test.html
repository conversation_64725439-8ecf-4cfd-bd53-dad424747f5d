<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>SafeHaven - CSS Test</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="./styles.css" />
    
    <style>
      body {
        font-family: 'Inter', sans-serif;
      }
    </style>
  </head>

  <body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-gray-900 mb-8 text-center">
          SafeHaven CSS Test
        </h1>
        
        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">Tailwind CSS Test</h2>
          <p class="text-gray-600 mb-4">
            If you can see this styled content, Tailwind CSS is working correctly!
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 class="text-lg font-medium text-blue-900 mb-2">Blue Card</h3>
              <p class="text-blue-700">This is a blue-themed card.</p>
            </div>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 class="text-lg font-medium text-green-900 mb-2">Green Card</h3>
              <p class="text-green-700">This is a green-themed card.</p>
            </div>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 class="text-lg font-medium text-red-900 mb-2">Red Card</h3>
              <p class="text-red-700">This is a red-themed card.</p>
            </div>
          </div>
          
          <div class="flex space-x-4">
            <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              Primary Button
            </button>
            <button class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              Secondary Button
            </button>
            <button class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
              Danger Button
            </button>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">Navigation Test</h2>
          <div class="flex space-x-4">
            <a href="index.html" class="text-blue-600 hover:text-blue-800 font-medium">
              Main App
            </a>
            <a href="standalone.html" class="text-blue-600 hover:text-blue-800 font-medium">
              Standalone App
            </a>
          </div>
        </div>
      </div>
    </div>
  </body>
</html> 