# SafeHaven Emergency Management Dashboard - Deployment Guide

## Overview
This guide provides comprehensive instructions for deploying the SafeHaven emergency management dashboard using Docker containers and Google Cloud Run. The deployment solution is designed for production-ready emergency management operations with high availability, scalability, and security.

## Architecture Overview

### Deployment Stack
- **Container Platform**: Docker with multi-stage builds
- **Hosting**: Google Cloud Run (serverless containers)
- **Registry**: Google Cloud Artifact Registry
- **CI/CD**: GitHub Actions + Google Cloud Build
- **Monitoring**: Built-in Cloud Run monitoring + optional Prometheus/Grafana
- **Security**: Secret Manager for environment variables

### Environment Structure
- **Production**: `safehaven-dashboard` (main service)
- **Staging**: `safehaven-dashboard-staging` (testing environment)
- **Development**: Local Docker containers with hot reload

## Prerequisites

### Required Accounts & Services
1. **Google Cloud Platform Account**
   - Project with billing enabled
   - Cloud Run API enabled
   - Artifact Registry API enabled
   - Secret Manager API enabled
   - Cloud Build API enabled

2. **GitHub Repository**
   - Repository with admin access
   - GitHub Actions enabled

3. **Firebase Project**
   - Authentication enabled
   - Firestore database configured
   - Realtime Database enabled

4. **External Services**
   - Google Maps API key
   - Twilio account (for SMS alerts)

### Required Tools (for local development)
- Docker Desktop 4.0+
- Node.js 18+
- Google Cloud SDK
- Git

## Environment Variables

### Required Secrets (Google Cloud Secret Manager)
Create the following secrets in Google Cloud Secret Manager:

```bash
# Firebase Configuration
gcloud secrets create firebase-api-key --data-file=<(echo "YOUR_FIREBASE_API_KEY")
gcloud secrets create firebase-auth-domain --data-file=<(echo "YOUR_PROJECT.firebaseapp.com")
gcloud secrets create firebase-project-id --data-file=<(echo "YOUR_PROJECT_ID")
gcloud secrets create firebase-storage-bucket --data-file=<(echo "YOUR_PROJECT.appspot.com")
gcloud secrets create firebase-messaging-sender-id --data-file=<(echo "YOUR_SENDER_ID")
gcloud secrets create firebase-app-id --data-file=<(echo "YOUR_APP_ID")
gcloud secrets create firebase-measurement-id --data-file=<(echo "YOUR_MEASUREMENT_ID")
gcloud secrets create firebase-database-url --data-file=<(echo "https://YOUR_PROJECT-default-rtdb.firebaseio.com")

# Google Maps Configuration
gcloud secrets create google-maps-api-key --data-file=<(echo "YOUR_GOOGLE_MAPS_API_KEY")

# Twilio Configuration
gcloud secrets create twilio-account-sid --data-file=<(echo "YOUR_TWILIO_ACCOUNT_SID")
gcloud secrets create twilio-auth-token --data-file=<(echo "YOUR_TWILIO_AUTH_TOKEN")
gcloud secrets create twilio-phone-number --data-file=<(echo "YOUR_TWILIO_PHONE_NUMBER")
```

### GitHub Secrets
Configure the following secrets in your GitHub repository:

```
GCP_PROJECT_ID=your-gcp-project-id
GCP_SA_KEY=<service-account-json-key>
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/... (optional)
```

## Local Development

### Using Docker Compose

1. **Clone the repository**:
```bash
git clone https://github.com/your-org/SafeHaven.git
cd SafeHaven
```

2. **Create local environment file**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start development environment**:
```bash
# Start development server with hot reload
docker-compose up safehaven-dev

# Or start production build for testing
docker-compose up safehaven-prod
```

4. **Access the application**:
- Development: http://localhost:19006
- Production: http://localhost:8080

### Using Docker directly

1. **Build development image**:
```bash
docker build --target development -t safehaven-dev .
```

2. **Run development container**:
```bash
docker run -p 19006:19006 -p 19001:19001 \
  -v $(pwd)/web:/app/web:ro \
  -v $(pwd)/src:/app/src:ro \
  --env-file .env \
  safehaven-dev
```

3. **Build production image**:
```bash
docker build --target production \
  --build-arg EXPO_PUBLIC_FIREBASE_API_KEY="your-api-key" \
  --build-arg EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN="your-domain" \
  # ... other build args
  -t safehaven-prod .
```

4. **Run production container**:
```bash
docker run -p 8080:8080 safehaven-prod
```

## Production Deployment

### Initial Setup

1. **Create Google Cloud Project**:
```bash
gcloud projects create safehaven-production
gcloud config set project safehaven-production
```

2. **Enable required APIs**:
```bash
gcloud services enable run.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable cloudbuild.googleapis.com
```

3. **Create Artifact Registry repository**:
```bash
gcloud artifacts repositories create safehaven-repo \
  --repository-format=docker \
  --location=us-central1 \
  --description="SafeHaven Emergency Dashboard"
```

4. **Create service account**:
```bash
gcloud iam service-accounts create safehaven-dashboard \
  --description="SafeHaven Dashboard Service Account" \
  --display-name="SafeHaven Dashboard"

# Grant necessary permissions
gcloud projects add-iam-policy-binding safehaven-production \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/run.invoker"

gcloud projects add-iam-policy-binding safehaven-production \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```

### Automated Deployment (Recommended)

1. **Push to main branch**:
```bash
git push origin main
```

2. **Monitor deployment**:
- Check GitHub Actions: https://github.com/your-org/SafeHaven/actions
- Monitor Cloud Build: https://console.cloud.google.com/cloud-build/builds
- Check Cloud Run: https://console.cloud.google.com/run

### Manual Deployment

1. **Build and deploy using Cloud Build**:
```bash
gcloud builds submit --config cloudbuild.yaml .
```

2. **Deploy specific image**:
```bash
gcloud run deploy safehaven-dashboard \
  --image us-central1-docker.pkg.dev/PROJECT_ID/safehaven-repo/safehaven-dashboard:latest \
  --region us-central1 \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --min-instances 1 \
  --max-instances 100 \
  --port 8080
```

## Configuration

### Resource Limits
- **Production**: 2 CPU, 2Gi memory, 1-100 instances
- **Staging**: 1 CPU, 1Gi memory, 0-10 instances
- **Development**: 0.5 CPU, 512Mi memory, 0-1 instances

### Scaling Parameters
- **Target Concurrency**: 80 requests per instance
- **Request Timeout**: 300 seconds (for emergency operations)
- **Startup Timeout**: 30 seconds

### Health Checks
- **Endpoint**: `/health`
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Failure Threshold**: 3 attempts

## Monitoring & Logging

### Built-in Monitoring
- **Cloud Run Metrics**: CPU, memory, request count, latency
- **Cloud Logging**: Application logs, access logs, error logs
- **Error Reporting**: Automatic error detection and alerting

### Custom Monitoring (Optional)
Use the provided Docker Compose profiles for advanced monitoring:

```bash
# Start with monitoring stack
docker-compose --profile monitoring up
```

Access monitoring dashboards:
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000 (admin/admin123)

### Log Analysis
```bash
# View recent logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=safehaven-dashboard" \
  --limit=50 \
  --format="table(timestamp,severity,textPayload)"

# Filter error logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=safehaven-dashboard AND severity>=ERROR" \
  --limit=20
```

## Security

### Container Security
- **Non-root user**: Containers run as nginx user (UID 1001)
- **Read-only filesystem**: Application files are read-only
- **Minimal attack surface**: Alpine Linux base with minimal packages
- **Security scanning**: Automatic vulnerability scanning in CI/CD

### Network Security
- **HTTPS only**: Automatic SSL certificate provisioning
- **VPC connector**: Optional private network access
- **IAM policies**: Least privilege access control

### Secret Management
- **Google Secret Manager**: All sensitive data stored securely
- **No secrets in code**: Environment variables injected at build time
- **Rotation support**: Easy secret rotation without code changes

## Troubleshooting

### Common Issues

#### 1. Build Failures
**Symptom**: Docker build fails with dependency errors
**Solution**:
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker build --no-cache --target production -t safehaven .
```

#### 2. Deployment Timeouts
**Symptom**: Cloud Run deployment times out
**Solution**:
```bash
# Check build logs
gcloud builds log BUILD_ID

# Increase timeout in cloudbuild.yaml
timeout: '2400s'  # 40 minutes
```

#### 3. Health Check Failures
**Symptom**: Service fails health checks
**Solution**:
```bash
# Check container logs
gcloud logs read "resource.type=cloud_run_revision" --limit=50

# Test health endpoint locally
curl -f http://localhost:8080/health
```

#### 4. Environment Variable Issues
**Symptom**: Application fails to start with configuration errors
**Solution**:
```bash
# Verify secrets exist
gcloud secrets list

# Check secret values
gcloud secrets versions access latest --secret="firebase-api-key"

# Update secret
echo "new-value" | gcloud secrets versions add firebase-api-key --data-file=-
```

### Performance Issues

#### 1. Slow Response Times
**Symptoms**: High latency, timeouts
**Solutions**:
- Increase CPU allocation: `--cpu 2`
- Increase memory: `--memory 2Gi`
- Adjust concurrency: `--concurrency 50`
- Enable CPU boost: `--cpu-boost`

#### 2. Cold Start Issues
**Symptoms**: First request takes long time
**Solutions**:
- Set minimum instances: `--min-instances 1`
- Use startup CPU boost
- Optimize Docker image size

### Emergency Procedures

#### 1. Immediate Rollback
```bash
# Get previous revision
PREV_REVISION=$(gcloud run revisions list --service=safehaven-dashboard --region=us-central1 --limit=2 --format='value(metadata.name)' | tail -n 1)

# Rollback traffic
gcloud run services update-traffic safehaven-dashboard \
  --to-revisions=$PREV_REVISION=100 \
  --region=us-central1
```

#### 2. Scale Up for Emergency
```bash
# Increase capacity immediately
gcloud run services update safehaven-dashboard \
  --min-instances=10 \
  --max-instances=500 \
  --region=us-central1
```

#### 3. Enable Debug Mode
```bash
# Deploy with debug logging
gcloud run services update safehaven-dashboard \
  --set-env-vars="LOG_LEVEL=debug" \
  --region=us-central1
```

## Maintenance

### Regular Tasks
- **Weekly**: Review logs and metrics
- **Monthly**: Update dependencies and base images
- **Quarterly**: Security audit and penetration testing
- **Annually**: Disaster recovery testing

### Updates
1. **Security Updates**: Automatic via Dependabot
2. **Feature Updates**: Via pull requests and CI/CD
3. **Infrastructure Updates**: Manual review and approval

### Backup & Recovery
- **Database**: Firebase automatic backups
- **Configuration**: Version controlled in Git
- **Secrets**: Backed up in Secret Manager
- **Container Images**: Stored in Artifact Registry with retention policy

## Support

### Documentation
- **API Documentation**: `/docs` endpoint
- **Health Status**: `/health` endpoint
- **Metrics**: Cloud Run console

### Contact Information
- **Emergency Contact**: <EMAIL>
- **Technical Support**: <EMAIL>
- **On-call Engineer**: +1-XXX-XXX-XXXX

### Escalation Procedures
1. **Level 1**: Application issues → Development team
2. **Level 2**: Infrastructure issues → DevOps team
3. **Level 3**: Critical emergencies → Emergency coordinator

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Maintained by**: SafeHaven DevOps Team
