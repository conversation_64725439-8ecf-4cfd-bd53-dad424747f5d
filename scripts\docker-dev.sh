#!/bin/bash

# SafeHaven Emergency Management Dashboard
# Docker Development Helper Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="safehaven"
DEV_PORT=19006
PROD_PORT=8080
COMPOSE_FILE="docker-compose.yml"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker Desktop."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose."
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi
    
    log_success "All requirements met!"
}

check_env_file() {
    if [ ! -f ".env" ]; then
        log_warning ".env file not found. Creating from template..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_info "Please edit .env file with your configuration before continuing."
            log_info "Required variables: EXPO_PUBLIC_FIREBASE_*, EXPO_PUBLIC_GOOGLE_MAPS_API_KEY, etc."
            read -p "Press Enter after editing .env file..."
        else
            log_error ".env.example not found. Please create .env file manually."
            exit 1
        fi
    fi
}

build_images() {
    log_info "Building Docker images..."
    
    # Build development image
    log_info "Building development image..."
    docker-compose build safehaven-dev
    
    # Build production image
    log_info "Building production image..."
    docker-compose build safehaven-prod
    
    log_success "Images built successfully!"
}

start_development() {
    log_info "Starting development environment..."
    
    # Stop any existing containers
    docker-compose down
    
    # Start development container
    docker-compose up -d safehaven-dev
    
    log_success "Development server started!"
    log_info "Access the application at: http://localhost:$DEV_PORT"
    log_info "Development tools at: http://localhost:19001"
    
    # Show logs
    log_info "Showing logs (Ctrl+C to stop)..."
    docker-compose logs -f safehaven-dev
}

start_production() {
    log_info "Starting production environment..."
    
    # Stop any existing containers
    docker-compose down
    
    # Start production container
    docker-compose up -d safehaven-prod
    
    log_success "Production server started!"
    log_info "Access the application at: http://localhost:$PROD_PORT"
    
    # Wait for health check
    log_info "Waiting for health check..."
    sleep 10
    
    if curl -f http://localhost:$PROD_PORT/health &> /dev/null; then
        log_success "Health check passed!"
    else
        log_warning "Health check failed. Check logs for issues."
    fi
    
    # Show logs
    log_info "Showing logs (Ctrl+C to stop)..."
    docker-compose logs -f safehaven-prod
}

start_monitoring() {
    log_info "Starting monitoring stack..."
    
    # Start monitoring services
    docker-compose --profile monitoring up -d
    
    log_success "Monitoring stack started!"
    log_info "Prometheus: http://localhost:9090"
    log_info "Grafana: http://localhost:3000 (admin/admin123)"
}

stop_services() {
    log_info "Stopping all services..."
    docker-compose down
    log_success "All services stopped!"
}

clean_up() {
    log_info "Cleaning up Docker resources..."
    
    # Stop all containers
    docker-compose down
    
    # Remove images
    docker-compose down --rmi all
    
    # Remove volumes
    docker-compose down --volumes
    
    # Prune system
    docker system prune -f
    
    log_success "Cleanup completed!"
}

show_logs() {
    local service=${1:-safehaven-dev}
    log_info "Showing logs for $service..."
    docker-compose logs -f "$service"
}

show_status() {
    log_info "Service status:"
    docker-compose ps
    
    echo ""
    log_info "Docker images:"
    docker images | grep safehaven
    
    echo ""
    log_info "Resource usage:"
    docker stats --no-stream
}

run_tests() {
    log_info "Running tests in container..."
    
    # Build test image
    docker build --target development -t safehaven-test .
    
    # Run tests
    docker run --rm \
        -v "$(pwd):/app" \
        -w /app \
        safehaven-test \
        npm test
    
    log_success "Tests completed!"
}

shell_access() {
    local service=${1:-safehaven-dev}
    log_info "Opening shell in $service container..."
    docker-compose exec "$service" /bin/sh
}

show_help() {
    echo "SafeHaven Docker Development Helper"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Start development environment with hot reload"
    echo "  prod        Start production environment for testing"
    echo "  build       Build Docker images"
    echo "  monitoring  Start monitoring stack (Prometheus + Grafana)"
    echo "  stop        Stop all services"
    echo "  clean       Clean up Docker resources"
    echo "  logs        Show logs [service-name]"
    echo "  status      Show service status and resource usage"
    echo "  test        Run tests in container"
    echo "  shell       Open shell in container [service-name]"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev                    # Start development server"
    echo "  $0 prod                   # Start production server"
    echo "  $0 logs safehaven-prod    # Show production logs"
    echo "  $0 shell safehaven-dev    # Open shell in dev container"
    echo ""
}

# Main script logic
main() {
    local command=${1:-help}
    
    case $command in
        "dev"|"development")
            check_requirements
            check_env_file
            start_development
            ;;
        "prod"|"production")
            check_requirements
            check_env_file
            start_production
            ;;
        "build")
            check_requirements
            check_env_file
            build_images
            ;;
        "monitoring"|"monitor")
            check_requirements
            start_monitoring
            ;;
        "stop")
            stop_services
            ;;
        "clean"|"cleanup")
            clean_up
            ;;
        "logs")
            show_logs "$2"
            ;;
        "status")
            show_status
            ;;
        "test"|"tests")
            check_requirements
            run_tests
            ;;
        "shell"|"bash")
            shell_access "$2"
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
