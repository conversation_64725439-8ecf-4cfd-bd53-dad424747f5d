<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>SafeHaven - CDN Test</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="./styles.css" />
    
    <style>
      body {
        font-family: 'Inter', sans-serif;
      }
    </style>
  </head>

  <body class="bg-gray-100 min-h-screen">
    <div id="root">
      <!-- Loading state -->
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- React Scripts from CDN -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <script type="text/babel">
      const { useState } = React;
      
      function SafeHavenApp() {
        const [currentPage, setCurrentPage] = useState('dashboard');
        const [loading, setLoading] = useState(false);
        
        const user = { email: '<EMAIL>', displayName: 'Test User' };
        
        const renderCurrentPage = () => {
          switch (currentPage) {
            case 'dashboard':
              return (
                <div className="p-6">
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">Dashboard</h1>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900">Active Alerts</h3>
                      <p className="text-3xl font-bold text-blue-600">12</p>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900">Emergency Reports</h3>
                      <p className="text-3xl font-bold text-red-600">5</p>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900">Available Shelters</h3>
                      <p className="text-3xl font-bold text-green-600">8</p>
                    </div>
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900">SOS Messages</h3>
                      <p className="text-3xl font-bold text-orange-600">3</p>
                    </div>
                  </div>
                </div>
              );
            case 'alerts':
              return (
                <div className="p-6">
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">Alerts</h1>
                  <p className="text-gray-600">Alerts page content will go here.</p>
                </div>
              );
            default:
              return (
                <div className="p-6">
                  <h1 className="text-2xl font-bold text-gray-900 mb-4">Dashboard</h1>
                  <p className="text-gray-600">Welcome to SafeHaven Emergency Management System.</p>
                </div>
              );
          }
        };

        if (loading) {
          return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center">
              <div className="text-center">
                <div className="relative">
                  <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
                  <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-400 animate-ping"></div>
                </div>
                <p className="mt-6 text-lg font-medium text-gray-700">Loading SafeHaven...</p>
                <p className="mt-2 text-sm text-gray-500">Emergency Management System</p>
              </div>
            </div>
          );
        }

        return (
          <div className="min-h-screen bg-gray-50">
            <div className="flex h-screen overflow-hidden">
              {/* Sidebar */}
              <div className="w-64 bg-white shadow-lg border-r border-gray-200 flex-shrink-0">
                <div className="p-6">
                  <h1 className="text-xl font-bold text-gray-900">SafeHaven</h1>
                  <p className="text-sm text-gray-600">Emergency Management</p>
                </div>
                <nav className="mt-6">
                  <button
                    onClick={() => setCurrentPage('dashboard')}
                    className={`w-full text-left px-4 py-2 rounded-lg ${
                      currentPage === 'dashboard' 
                        ? 'bg-blue-50 text-blue-700' 
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    Dashboard
                  </button>
                  <button
                    onClick={() => setCurrentPage('alerts')}
                    className={`w-full text-left px-4 py-2 rounded-lg ${
                      currentPage === 'alerts' 
                        ? 'bg-blue-50 text-blue-700' 
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    Alerts
                  </button>
                </nav>
              </div>

              {/* Main Content Area */}
              <div className="flex-1 flex flex-col overflow-hidden">
                {/* Header */}
                <header className="bg-white shadow-sm border-b border-gray-200">
                  <div className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <h2 className="text-lg font-semibold text-gray-900">
                        {currentPage === 'dashboard' ? 'Dashboard' : 'Alerts'}
                      </h2>
                      <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-600">
                          Welcome, {user.displayName}
                        </span>
                        <button
                          onClick={() => console.log('Logout clicked')}
                          className="px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
                        >
                          Sign Out
                        </button>
                      </div>
                    </div>
                  </div>
                </header>
                
                {/* Main Content */}
                <main className="flex-1 overflow-y-auto bg-gray-50">
                  {renderCurrentPage()}
                </main>
              </div>
            </div>
          </div>
        );
      }
      
      console.log('CDN React app starting...');
      const rootElement = document.getElementById('root');
      if (rootElement) {
        const root = ReactDOM.createRoot(rootElement);
        root.render(<SafeHavenApp />);
        console.log('CDN React app rendered successfully');
      } else {
        console.error('Root element not found');
      }
    </script>
  </body>
</html> 