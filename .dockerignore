# SafeHaven Docker Build Optimization
# Exclude unnecessary files from Docker build context

# Version control
.git
.github
.gitignore
.gitattributes

# IDE and editor files
.vscode
.idea
.idx
*.swp
*.swo
*~

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# Build outputs (will be created during build)
dist
build
.expo
.expo-shared

# Development environment files
.env.local
.env.development
.env.test
.env.production

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md
docs/

# Testing
coverage/
.nyc_output
.jest
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Logs
logs
*.log

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.circleci/
.travis.yml

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Temporary files
tmp/
temp/
.tmp/

# Cache files
.cache
.parcel-cache
.tailwindcss-cache
.postcss-cache

# Development scripts
scripts/dev/
scripts/test/

# Examples and demos
examples/
demo/
demos/
samples/

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar
*.tar.gz

# Source maps (exclude from production)
*.map
*.d.ts.map
