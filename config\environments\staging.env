# SafeHaven Emergency Management Dashboard
# Staging Environment Configuration

# Application Settings
NODE_ENV=staging
PORT=8080
LOG_LEVEL=debug

# Security Settings
HELMET_ENABLED=true
CORS_ENABLED=true
RATE_LIMITING_ENABLED=false
SESSION_SECURE=true

# Performance Settings
COMPRESSION_ENABLED=true
CACHE_ENABLED=false
CACHE_TTL=300

# Monitoring Settings
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
ERROR_REPORTING_ENABLED=true

# Database Settings
DB_POOL_SIZE=5
DB_TIMEOUT=15000
DB_RETRY_ATTEMPTS=2

# External Services
TWILIO_ENABLED=true
GOOGLE_MAPS_ENABLED=true
FIREBASE_ENABLED=true

# Alert Settings
DEFAULT_ALERT_RADIUS_KM=5
MAX_ALERT_RADIUS_KM=25
ALERT_BATCH_SIZE=50
ALERT_RETRY_ATTEMPTS=2

# File Upload Settings
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200

# Session Settings
SESSION_TIMEOUT=1800000
SESSION_CLEANUP_INTERVAL=300000

# Backup Settings
BACKUP_ENABLED=false
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=7

# Feature Flags
FEATURE_REAL_TIME_ALERTS=true
FEATURE_GEOFENCING=true
FEATURE_MULTI_LANGUAGE=false
FEATURE_DARK_MODE=true
FEATURE_OFFLINE_MODE=false

# Cloud Run Specific
CLOUD_RUN_SERVICE_NAME=safehaven-dashboard-staging
CLOUD_RUN_REGION=us-central1
CLOUD_RUN_MIN_INSTANCES=0
CLOUD_RUN_MAX_INSTANCES=10
CLOUD_RUN_CONCURRENCY=50
CLOUD_RUN_TIMEOUT=300

# Scaling Settings
AUTO_SCALING_ENABLED=true
SCALE_UP_THRESHOLD=70
SCALE_DOWN_THRESHOLD=30
SCALE_UP_COOLDOWN=180
SCALE_DOWN_COOLDOWN=300
