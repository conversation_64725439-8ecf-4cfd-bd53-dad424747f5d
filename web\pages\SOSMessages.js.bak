import React, { useState, useEffect } from 'react';
import { collection, query, orderBy, limit, onSnapshot, doc, updateDoc } from 'firebase/firestore';
import { firestore } from '../../src/config/firebase';
import { SOSStatus } from '../../src/services/sos';

const SOSMessages = () => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedMessage, setSelectedMessage] = useState(null);

  useEffect(() => {
    // Simplified loading with sample data
    const timeoutId = setTimeout(() => {
      setLoading(false);
      setMessages([
        { id: 1, message: 'Help needed at location A', timestamp: new Date(), status: 'pending' },
        { id: 2, message: 'Medical emergency reported', timestamp: new Date(Date.now() - 1800000), status: 'responded' },
        { id: 3, message: 'Fire emergency assistance needed', timestamp: new Date(Date.now() - 3600000), status: 'resolved' }
      ]);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner"></div>
        <span className="ml-2">Loading SOS messages...</span>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">SOS Messages</h1>
        <p className="text-gray-600">Emergency assistance requests</p>
      </div>

      <div className="grid gap-4">
        {messages.map(message => (
          <div key={message.id} className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">{message.message}</p>
                <p className="text-sm text-gray-600">
                  {message.timestamp.toLocaleString()}
                </p>
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                message.status === 'pending' ? 'bg-red-100 text-red-800' :
                message.status === 'responded' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {message.status.toUpperCase()}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

export default SOSMessages;

