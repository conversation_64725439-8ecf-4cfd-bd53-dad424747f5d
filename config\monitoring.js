/**
 * SafeHaven Emergency Management Dashboard
 * Monitoring and Logging Configuration
 */

const configLoader = require('./config-loader');

class MonitoringConfig {
  constructor() {
    this.config = configLoader.getAll();
    this.setupLogging();
    this.setupMetrics();
    this.setupHealthChecks();
  }

  /**
   * Setup structured logging
   */
  setupLogging() {
    // Configure console logging with structured format
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    console.log = (...args) => {
      const logEntry = this.createLogEntry('INFO', args);
      originalConsoleLog(JSON.stringify(logEntry));
    };

    console.error = (...args) => {
      const logEntry = this.createLogEntry('ERROR', args);
      originalConsoleError(JSON.stringify(logEntry));
    };

    console.warn = (...args) => {
      const logEntry = this.createLogEntry('WARN', args);
      originalConsoleWarn(JSON.stringify(logEntry));
    };

    // Setup error tracking
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      this.reportError(error, 'uncaughtException');
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.reportError(reason, 'unhandledRejection');
    });
  }

  /**
   * Create structured log entry
   */
  createLogEntry(level, args) {
    return {
      timestamp: new Date().toISOString(),
      level,
      service: 'safehaven-dashboard',
      environment: this.config.environment,
      message: args.join(' '),
      pid: process.pid,
      memory: process.memoryUsage(),
      uptime: process.uptime()
    };
  }

  /**
   * Setup application metrics
   */
  setupMetrics() {
    if (!this.config.monitoring.metricsEnabled) {
      return;
    }

    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        error: 0,
        responseTime: []
      },
      alerts: {
        sent: 0,
        failed: 0,
        pending: 0
      },
      users: {
        active: 0,
        registered: 0
      },
      system: {
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        cpu: process.cpuUsage()
      }
    };

    // Update system metrics every 30 seconds
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000);
  }

  /**
   * Update system metrics
   */
  updateSystemMetrics() {
    this.metrics.system = {
      memory: process.memoryUsage(),
      uptime: process.uptime(),
      cpu: process.cpuUsage(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Record request metrics
   */
  recordRequest(req, res, responseTime) {
    if (!this.config.monitoring.metricsEnabled) {
      return;
    }

    this.metrics.requests.total++;
    this.metrics.requests.responseTime.push(responseTime);

    if (res.statusCode >= 200 && res.statusCode < 400) {
      this.metrics.requests.success++;
    } else {
      this.metrics.requests.error++;
    }

    // Keep only last 1000 response times
    if (this.metrics.requests.responseTime.length > 1000) {
      this.metrics.requests.responseTime = this.metrics.requests.responseTime.slice(-1000);
    }

    // Log slow requests
    if (responseTime > 5000) {
      console.warn(`Slow request detected: ${req.method} ${req.url} - ${responseTime}ms`);
    }
  }

  /**
   * Record alert metrics
   */
  recordAlert(type, status) {
    if (!this.config.monitoring.metricsEnabled) {
      return;
    }

    switch (status) {
      case 'sent':
        this.metrics.alerts.sent++;
        break;
      case 'failed':
        this.metrics.alerts.failed++;
        break;
      case 'pending':
        this.metrics.alerts.pending++;
        break;
    }
  }

  /**
   * Setup health checks
   */
  setupHealthChecks() {
    this.healthChecks = {
      database: () => this.checkDatabase(),
      firebase: () => this.checkFirebase(),
      externalServices: () => this.checkExternalServices(),
      memory: () => this.checkMemory(),
      disk: () => this.checkDisk()
    };
  }

  /**
   * Run all health checks
   */
  async runHealthChecks() {
    const results = {};
    const startTime = Date.now();

    for (const [name, check] of Object.entries(this.healthChecks)) {
      try {
        const checkStart = Date.now();
        const result = await check();
        const checkTime = Date.now() - checkStart;
        
        results[name] = {
          status: result ? 'healthy' : 'unhealthy',
          responseTime: checkTime,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        results[name] = {
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }

    const totalTime = Date.now() - startTime;
    const overallStatus = Object.values(results).every(r => r.status === 'healthy') ? 'healthy' : 'unhealthy';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      responseTime: totalTime,
      checks: results,
      version: this.config.app.version,
      environment: this.config.environment,
      uptime: process.uptime()
    };
  }

  /**
   * Check database connectivity
   */
  async checkDatabase() {
    // Implement database health check
    // For Firebase, this could be a simple read operation
    return true;
  }

  /**
   * Check Firebase connectivity
   */
  async checkFirebase() {
    // Implement Firebase health check
    return true;
  }

  /**
   * Check external services
   */
  async checkExternalServices() {
    // Check Twilio, Google Maps, etc.
    return true;
  }

  /**
   * Check memory usage
   */
  checkMemory() {
    const usage = process.memoryUsage();
    const maxMemory = 2 * 1024 * 1024 * 1024; // 2GB
    return usage.heapUsed < maxMemory * 0.9; // Alert if using more than 90%
  }

  /**
   * Check disk usage
   */
  checkDisk() {
    // Implement disk usage check if needed
    return true;
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    if (!this.config.monitoring.metricsEnabled) {
      return { message: 'Metrics disabled' };
    }

    const avgResponseTime = this.metrics.requests.responseTime.length > 0
      ? this.metrics.requests.responseTime.reduce((a, b) => a + b, 0) / this.metrics.requests.responseTime.length
      : 0;

    return {
      ...this.metrics,
      requests: {
        ...this.metrics.requests,
        averageResponseTime: Math.round(avgResponseTime),
        successRate: this.metrics.requests.total > 0
          ? Math.round((this.metrics.requests.success / this.metrics.requests.total) * 100)
          : 0
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Report error to monitoring service
   */
  reportError(error, context = '') {
    if (!this.config.monitoring.errorReportingEnabled) {
      return;
    }

    const errorReport = {
      timestamp: new Date().toISOString(),
      service: 'safehaven-dashboard',
      environment: this.config.environment,
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      context,
      system: {
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        pid: process.pid
      }
    };

    // In production, this would send to Google Cloud Error Reporting
    console.error('Error Report:', JSON.stringify(errorReport));
  }

  /**
   * Create Express middleware for request monitoring
   */
  createRequestMiddleware() {
    return (req, res, next) => {
      const startTime = Date.now();

      // Log request
      console.log(`${req.method} ${req.url} - ${req.ip}`);

      // Override res.end to capture response time
      const originalEnd = res.end;
      res.end = (...args) => {
        const responseTime = Date.now() - startTime;
        this.recordRequest(req, res, responseTime);
        originalEnd.apply(res, args);
      };

      next();
    };
  }

  /**
   * Create Express middleware for error monitoring
   */
  createErrorMiddleware() {
    return (err, req, res, next) => {
      this.reportError(err, `${req.method} ${req.url}`);
      next(err);
    };
  }
}

// Create singleton instance
const monitoring = new MonitoringConfig();

module.exports = monitoring;
