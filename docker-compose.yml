# SafeHaven Emergency Management Dashboard
# Docker Compose configuration for local development and testing

version: '3.8'

services:
  # Production build for testing
  safehaven-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        - EXPO_PUBLIC_FIREBASE_API_KEY=${EXPO_PUBLIC_FIREBASE_API_KEY}
        - EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=${EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN}
        - EXPO_PUBLIC_FIREBASE_PROJECT_ID=${EXPO_PUBLIC_FIREBASE_PROJECT_ID}
        - EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=${EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET}
        - EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}
        - EXPO_PUBLIC_FIREBASE_APP_ID=${EXPO_PUBLIC_FIREBASE_APP_ID}
        - EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=${EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID}
        - EXPO_PUBLIC_FIREBASE_DATABASE_URL=${EXPO_PUBLIC_FIREBASE_DATABASE_URL}
        - EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=${EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}
        - EXPO_PUBLIC_TWILIO_ACCOUNT_SID=${EXPO_PUBLIC_TWILIO_ACCOUNT_SID}
        - EXPO_PUBLIC_TWILIO_AUTH_TOKEN=${EXPO_PUBLIC_TWILIO_AUTH_TOKEN}
        - EXPO_PUBLIC_TWILIO_PHONE_NUMBER=${EXPO_PUBLIC_TWILIO_PHONE_NUMBER}
        - EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=${EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM:-5}
    container_name: safehaven-prod
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    restart: unless-stopped
    networks:
      - safehaven-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.safehaven-prod.rule=Host(`localhost`)"
      - "traefik.http.services.safehaven-prod.loadbalancer.server.port=8080"

  # Development environment with hot reload
  safehaven-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: safehaven-dev
    ports:
      - "19006:19006"  # Expo dev server
      - "19001:19001"  # Expo dev tools
    environment:
      - NODE_ENV=development
      - EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
      - EXPO_PUBLIC_FIREBASE_API_KEY=${EXPO_PUBLIC_FIREBASE_API_KEY}
      - EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=${EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN}
      - EXPO_PUBLIC_FIREBASE_PROJECT_ID=${EXPO_PUBLIC_FIREBASE_PROJECT_ID}
      - EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=${EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET}
      - EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}
      - EXPO_PUBLIC_FIREBASE_APP_ID=${EXPO_PUBLIC_FIREBASE_APP_ID}
      - EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=${EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID}
      - EXPO_PUBLIC_FIREBASE_DATABASE_URL=${EXPO_PUBLIC_FIREBASE_DATABASE_URL}
      - EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=${EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}
      - EXPO_PUBLIC_TWILIO_ACCOUNT_SID=${EXPO_PUBLIC_TWILIO_ACCOUNT_SID}
      - EXPO_PUBLIC_TWILIO_AUTH_TOKEN=${EXPO_PUBLIC_TWILIO_AUTH_TOKEN}
      - EXPO_PUBLIC_TWILIO_PHONE_NUMBER=${EXPO_PUBLIC_TWILIO_PHONE_NUMBER}
      - EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=${EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM:-5}
    volumes:
      # Mount source code for hot reload
      - ./web:/app/web:ro
      - ./src:/app/src:ro
      - ./package.json:/app/package.json:ro
      - ./package-lock.json:/app/package-lock.json:ro
      - ./tailwind.config.js:/app/tailwind.config.js:ro
      - ./postcss.config.js:/app/postcss.config.js:ro
      - ./app.json:/app/app.json:ro
      - ./expo.json:/app/expo.json:ro
      # Exclude node_modules to use container's version
      - /app/node_modules
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:19006"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    networks:
      - safehaven-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.safehaven-dev.rule=Host(`dev.localhost`)"
      - "traefik.http.services.safehaven-dev.loadbalancer.server.port=19006"

  # Nginx reverse proxy for local development
  nginx-proxy:
    image: nginx:1.25-alpine
    container_name: safehaven-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - safehaven-prod
    networks:
      - safehaven-network
    restart: unless-stopped
    profiles:
      - proxy

  # Redis for session management and caching
  redis:
    image: redis:7-alpine
    container_name: safehaven-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-safehaven123}
    networks:
      - safehaven-network
    restart: unless-stopped
    profiles:
      - cache

  # PostgreSQL for additional data storage (if needed)
  postgres:
    image: postgres:15-alpine
    container_name: safehaven-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-safehaven}
      - POSTGRES_USER=${POSTGRES_USER:-safehaven}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-safehaven123}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - safehaven-network
    restart: unless-stopped
    profiles:
      - database

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: safehaven-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - safehaven-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: safehaven-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - safehaven-network
    restart: unless-stopped
    profiles:
      - monitoring

networks:
  safehaven-network:
    driver: bridge
    name: safehaven-network

volumes:
  redis-data:
    name: safehaven-redis-data
  postgres-data:
    name: safehaven-postgres-data
  prometheus-data:
    name: safehaven-prometheus-data
  grafana-data:
    name: safehaven-grafana-data
