# SafeHaven Emergency Management Dashboard - Production Dockerfile
# Multi-stage build for optimized production deployment

# Stage 1: Build Dependencies and CSS
FROM node:18-alpine AS dependencies
LABEL stage=dependencies
LABEL description="Install dependencies and build Tailwind CSS"
LABEL maintainer="SafeHaven Team"
LABEL org.opencontainers.image.source="https://github.com/your-org/SafeHaven"

# Set working directory
WORKDIR /app

# Create non-root user early
RUN addgroup -g 1001 -S nodejs && \
    adduser -S -D -H -u 1001 -h /app -s /sbin/nologin -G nodejs -g nodejs nodejs

# Install system dependencies for node-gyp and native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY --chown=nodejs:nodejs package*.json ./

# Install all dependencies (including dev dependencies for CSS build)
RUN npm ci --silent && \
    npm cache clean --force

# Copy source files needed for CSS build
COPY --chown=nodejs:nodejs web/styles.css ./web/styles.css
COPY --chown=nodejs:nodejs tailwind.config.js ./
COPY --chown=nodejs:nodejs postcss.config.cjs ./

# Build Tailwind CSS for production
RUN npm run build:css-prod

# Stage 2: Build Application
FROM node:18-alpine AS builder
LABEL stage=builder
LABEL description="Build React/Expo web application"

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S -D -H -u 1001 -h /app -s /sbin/nologin -G nodejs -g nodejs nodejs

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Define ARGs for build-time environment variables
ARG EXPO_PUBLIC_FIREBASE_API_KEY
ARG EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN
ARG EXPO_PUBLIC_FIREBASE_PROJECT_ID
ARG EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET
ARG EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
ARG EXPO_PUBLIC_FIREBASE_APP_ID
ARG EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID
ARG EXPO_PUBLIC_FIREBASE_DATABASE_URL
ARG EXPO_PUBLIC_GOOGLE_MAPS_API_KEY
ARG EXPO_PUBLIC_TWILIO_ACCOUNT_SID
ARG EXPO_PUBLIC_TWILIO_AUTH_TOKEN
ARG EXPO_PUBLIC_TWILIO_PHONE_NUMBER
ARG EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM

# Set environment variables for build
ENV NODE_ENV=production
ENV EXPO_USE_FAST_RESOLVER=1
ENV EXPO_USE_METRO_WORKSPACE_ROOT=1
ENV EXPO_PUBLIC_FIREBASE_API_KEY=${EXPO_PUBLIC_FIREBASE_API_KEY}
ENV EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=${EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN}
ENV EXPO_PUBLIC_FIREBASE_PROJECT_ID=${EXPO_PUBLIC_FIREBASE_PROJECT_ID}
ENV EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=${EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET}
ENV EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}
ENV EXPO_PUBLIC_FIREBASE_APP_ID=${EXPO_PUBLIC_FIREBASE_APP_ID}
ENV EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=${EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID}
ENV EXPO_PUBLIC_FIREBASE_DATABASE_URL=${EXPO_PUBLIC_FIREBASE_DATABASE_URL}
ENV EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=${EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}
ENV EXPO_PUBLIC_TWILIO_ACCOUNT_SID=${EXPO_PUBLIC_TWILIO_ACCOUNT_SID}
ENV EXPO_PUBLIC_TWILIO_AUTH_TOKEN=${EXPO_PUBLIC_TWILIO_AUTH_TOKEN}
ENV EXPO_PUBLIC_TWILIO_PHONE_NUMBER=${EXPO_PUBLIC_TWILIO_PHONE_NUMBER}
ENV EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=${EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM}

# Copy package files
COPY --chown=nodejs:nodejs package*.json ./

# Copy dataconnect-generated directory if it exists
COPY --chown=nodejs:nodejs dataconnect-generated* ./dataconnect-generated/

# Install all dependencies including dev dependencies for build
RUN npm ci --silent

# Copy built CSS from dependencies stage
COPY --from=dependencies --chown=nodejs:nodejs /app/dist/styles.css ./dist/styles.css

# Copy the rest of the application source code
COPY --chown=nodejs:nodejs . .

# Create .env file from build arguments
RUN echo "EXPO_PUBLIC_FIREBASE_API_KEY=${EXPO_PUBLIC_FIREBASE_API_KEY}" > .env && \
    echo "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=${EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_PROJECT_ID=${EXPO_PUBLIC_FIREBASE_PROJECT_ID}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=${EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_APP_ID=${EXPO_PUBLIC_FIREBASE_APP_ID}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=${EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_DATABASE_URL=${EXPO_PUBLIC_FIREBASE_DATABASE_URL}" >> .env && \
    echo "EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=${EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}" >> .env && \
    echo "EXPO_PUBLIC_TWILIO_ACCOUNT_SID=${EXPO_PUBLIC_TWILIO_ACCOUNT_SID}" >> .env && \
    echo "EXPO_PUBLIC_TWILIO_AUTH_TOKEN=${EXPO_PUBLIC_TWILIO_AUTH_TOKEN}" >> .env && \
    echo "EXPO_PUBLIC_TWILIO_PHONE_NUMBER=${EXPO_PUBLIC_TWILIO_PHONE_NUMBER}" >> .env && \
    echo "EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=${EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM}" >> .env

# Build the web application
RUN npm run build:web

# Copy custom HTML template if it exists
RUN if [ -f web/index.html ]; then cp web/index.html dist/index.html; fi

# Stage 3: Production Server with Node.js
FROM node:18-alpine AS production
LABEL maintainer="SafeHaven Team"
LABEL description="SafeHaven Emergency Management Dashboard"
LABEL version="1.0.0"
LABEL org.opencontainers.image.source="https://github.com/your-org/SafeHaven"

# Install additional tools for health checks and debugging
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Set timezone for emergency management operations
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S -D -H -u 1001 -h /app -s /sbin/nologin -G nodejs -g nodejs nodejs

# Set working directory
WORKDIR /app

# Copy production dependencies from dependencies stage
COPY --from=dependencies --chown=nodejs:nodejs /app/node_modules ./node_modules

# Copy built application from builder stage
COPY --from=builder --chown=nodejs:nodejs /app/dist ./dist

# Copy server files
COPY --chown=nodejs:nodejs server/index.cjs ./server/
COPY --chown=nodejs:nodejs package*.json ./

# Set production environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Create necessary directories and set permissions
RUN mkdir -p /app/logs && \
    chown -R nodejs:nodejs /app && \
    chmod -R 755 /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the Node.js server
CMD ["node", "server/index.cjs"]

# Stage 4: Development Server (optional)
FROM node:18-alpine AS development
LABEL stage=development
LABEL description="Development environment with hot reload"

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Copy package files
COPY package*.json ./

# Install all dependencies including dev dependencies
RUN npm ci --silent

# Copy source files
COPY . .

# Expose development ports
EXPOSE 19006 19001

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:19006 || exit 1

# Start development server
CMD ["npm", "run", "dev"]
