# SafeHaven Emergency Management Dashboard - Production Dockerfile
# Multi-stage build for optimized production deployment

# Stage 1: Build Dependencies and CSS
FROM node:18-alpine AS dependencies
LABEL stage=dependencies
LABEL description="Install dependencies and build Tailwind CSS"

# Set working directory
WORKDIR /app

# Install system dependencies for node-gyp and native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Copy package files
COPY package*.json ./

# Install dependencies with npm ci for faster, reliable builds
RUN npm ci --only=production --silent && \
    npm cache clean --force

# Copy source files needed for CSS build
COPY web/styles.css ./web/styles.css
COPY tailwind.config.js ./
COPY postcss.config.js ./

# Build Tailwind CSS for production
RUN npm run build:css-prod

# Stage 2: Build Application
FROM node:18-alpine AS builder
LABEL stage=builder
LABEL description="Build React/Expo web application"

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Define ARGs for build-time environment variables
ARG EXPO_PUBLIC_FIREBASE_API_KEY
ARG EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN
ARG EXPO_PUBLIC_FIREBASE_PROJECT_ID
ARG EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET
ARG EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID
ARG EXPO_PUBLIC_FIREBASE_APP_ID
ARG EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID
ARG EXPO_PUBLIC_FIREBASE_DATABASE_URL
ARG EXPO_PUBLIC_GOOGLE_MAPS_API_KEY
ARG EXPO_PUBLIC_TWILIO_ACCOUNT_SID
ARG EXPO_PUBLIC_TWILIO_AUTH_TOKEN
ARG EXPO_PUBLIC_TWILIO_PHONE_NUMBER
ARG EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM

# Set environment variables for build
ENV NODE_ENV=production
ENV EXPO_USE_FAST_RESOLVER=1
ENV EXPO_USE_METRO_WORKSPACE_ROOT=1
ENV EXPO_PUBLIC_FIREBASE_API_KEY=${EXPO_PUBLIC_FIREBASE_API_KEY}
ENV EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=${EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN}
ENV EXPO_PUBLIC_FIREBASE_PROJECT_ID=${EXPO_PUBLIC_FIREBASE_PROJECT_ID}
ENV EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=${EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET}
ENV EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}
ENV EXPO_PUBLIC_FIREBASE_APP_ID=${EXPO_PUBLIC_FIREBASE_APP_ID}
ENV EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=${EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID}
ENV EXPO_PUBLIC_FIREBASE_DATABASE_URL=${EXPO_PUBLIC_FIREBASE_DATABASE_URL}
ENV EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=${EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}
ENV EXPO_PUBLIC_TWILIO_ACCOUNT_SID=${EXPO_PUBLIC_TWILIO_ACCOUNT_SID}
ENV EXPO_PUBLIC_TWILIO_AUTH_TOKEN=${EXPO_PUBLIC_TWILIO_AUTH_TOKEN}
ENV EXPO_PUBLIC_TWILIO_PHONE_NUMBER=${EXPO_PUBLIC_TWILIO_PHONE_NUMBER}
ENV EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=${EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM}

# Copy package files
COPY package*.json ./

# Copy dataconnect-generated directory if it exists
COPY dataconnect-generated* ./dataconnect-generated/

# Install all dependencies including dev dependencies for build
RUN npm ci --silent

# Copy built CSS from dependencies stage
COPY --from=dependencies /app/dist/styles.css ./dist/styles.css

# Copy the rest of the application source code
COPY . .

# Create .env file from build arguments
RUN echo "EXPO_PUBLIC_FIREBASE_API_KEY=${EXPO_PUBLIC_FIREBASE_API_KEY}" > .env && \
    echo "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=${EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_PROJECT_ID=${EXPO_PUBLIC_FIREBASE_PROJECT_ID}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=${EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_APP_ID=${EXPO_PUBLIC_FIREBASE_APP_ID}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=${EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID}" >> .env && \
    echo "EXPO_PUBLIC_FIREBASE_DATABASE_URL=${EXPO_PUBLIC_FIREBASE_DATABASE_URL}" >> .env && \
    echo "EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=${EXPO_PUBLIC_GOOGLE_MAPS_API_KEY}" >> .env && \
    echo "EXPO_PUBLIC_TWILIO_ACCOUNT_SID=${EXPO_PUBLIC_TWILIO_ACCOUNT_SID}" >> .env && \
    echo "EXPO_PUBLIC_TWILIO_AUTH_TOKEN=${EXPO_PUBLIC_TWILIO_AUTH_TOKEN}" >> .env && \
    echo "EXPO_PUBLIC_TWILIO_PHONE_NUMBER=${EXPO_PUBLIC_TWILIO_PHONE_NUMBER}" >> .env && \
    echo "EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM=${EXPO_PUBLIC_DEFAULT_ALERT_RADIUS_KM}" >> .env

# Build the web application
RUN npm run build:web

# Copy custom HTML template
RUN cp web/index.html dist/index.html

# Stage 3: Production Server with nginx
FROM nginx:1.25-alpine AS production
LABEL maintainer="SafeHaven Team"
LABEL description="SafeHaven Emergency Management Dashboard"
LABEL version="1.0.0"

# Install additional tools for health checks and debugging
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata

# Set timezone for emergency management operations
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create nginx user and directories
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# Copy built application from builder stage
COPY --from=builder --chown=nginx:nginx /app/dist /usr/share/nginx/html

# Create nginx configuration directories
RUN mkdir -p /etc/nginx/conf.d /docker

# Create nginx configuration files
RUN echo 'user nginx;' > /etc/nginx/nginx.conf && \
    echo 'worker_processes auto;' >> /etc/nginx/nginx.conf && \
    echo 'error_log /var/log/nginx/error.log warn;' >> /etc/nginx/nginx.conf && \
    echo 'pid /var/run/nginx.pid;' >> /etc/nginx/nginx.conf && \
    echo 'events { worker_connections 1024; }' >> /etc/nginx/nginx.conf && \
    echo 'http {' >> /etc/nginx/nginx.conf && \
    echo '    include /etc/nginx/mime.types;' >> /etc/nginx/nginx.conf && \
    echo '    default_type application/octet-stream;' >> /etc/nginx/nginx.conf && \
    echo '    sendfile on;' >> /etc/nginx/nginx.conf && \
    echo '    keepalive_timeout 65;' >> /etc/nginx/nginx.conf && \
    echo '    gzip on;' >> /etc/nginx/nginx.conf && \
    echo '    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;' >> /etc/nginx/nginx.conf && \
    echo '    include /etc/nginx/conf.d/*.conf;' >> /etc/nginx/nginx.conf && \
    echo '}' >> /etc/nginx/nginx.conf

# Create default server configuration
RUN echo 'server {' > /etc/nginx/conf.d/default.conf && \
    echo '    listen 8080;' >> /etc/nginx/conf.d/default.conf && \
    echo '    server_name _;' >> /etc/nginx/conf.d/default.conf && \
    echo '    root /usr/share/nginx/html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    index index.html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    location / {' >> /etc/nginx/conf.d/default.conf && \
    echo '        try_files $uri $uri/ /index.html;' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Cache-Control "no-cache, no-store, must-revalidate";' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Pragma "no-cache";' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Expires "0";' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '    location /health {' >> /etc/nginx/conf.d/default.conf && \
    echo '        access_log off;' >> /etc/nginx/conf.d/default.conf && \
    echo '        return 200 "healthy\n";' >> /etc/nginx/conf.d/default.conf && \
    echo '        add_header Content-Type text/plain;' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '}' >> /etc/nginx/conf.d/default.conf

# Create nginx directories and set permissions
RUN mkdir -p /var/cache/nginx/client_temp \
             /var/cache/nginx/proxy_temp \
             /var/cache/nginx/fastcgi_temp \
             /var/cache/nginx/uwsgi_temp \
             /var/cache/nginx/scgi_temp \
             /var/log/nginx \
             /var/run && \
    chown -R nginx:nginx /var/cache/nginx \
                         /var/log/nginx \
                         /var/run \
                         /usr/share/nginx/html && \
    chmod -R 755 /var/cache/nginx \
                 /var/log/nginx \
                 /usr/share/nginx/html

# Switch to non-root user
USER nginx

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

# Stage 4: Development Server (optional)
FROM node:18-alpine AS development
LABEL stage=development
LABEL description="Development environment with hot reload"

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Copy package files
COPY package*.json ./

# Install all dependencies including dev dependencies
RUN npm ci --silent

# Copy source files
COPY . .

# Expose development ports
EXPOSE 19006 19001

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:19006 || exit 1

# Start development server
CMD ["npm", "run", "dev"]
